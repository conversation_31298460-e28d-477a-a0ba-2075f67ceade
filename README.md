<p style="text-align: center">
  <a href="http://nestjs.com/" target="blank"><img src="https://nestjs.com/img/logo-small.svg" width="120" alt="Nest Logo" /></a>
</p>

[circleci-image]: https://img.shields.io/circleci/build/github/nestjs/nest/master?token=abc123def456
[circleci-url]: https://circleci.com/gh/nestjs/nest

<p style="text-align: center">A progressive <a href="http://nodejs.org" target="_blank">Node.js</a> framework for building efficient and scalable server-side applications.</p>

## Description

[Nest](https://github.com/nestjs/nest) framework TypeScript starter repository.

## Project setup

```bash
$ pnpm install
```

## Compile and run the project

```bash
# development
$ pnpm run start

# watch mode
$ pnpm run start:dev

# production mode
$ pnpm run start:prod
```

#### VSCODE LAUNCH
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "Launch Program",
      "runtimeVersion": "20.17.0",
      "skipFiles": [
        "<node_internals>/**"
      ],
      "runtimeExecutable": "pnpm",
      "runtimeArgs": ["run", "start:debug"],
      "outputCapture": "std",
      "env": {
        "CHECKSUM_SALT": "%P6bQFDV15qeQKUV^JAr",
        "CROSS_ORIGIN": "http://localhost:8100",
        "EVENT_COUNT": "50",
        "MAX_WORKERS": "2",
        "MY_POD_NAME": "pod_name_dev",
        "KAFKA_HOST": "localhost:9092",
        "KAFKA_TOPIC": "dgw.events.user-action",
        "NODE_ENV": "dev",
        "ENABLE_API": "true",
        "AWS_S3_BUCKET_NAME": "housesigma-dev-user-data",
        "AWS_ACCESS_KEY_ID": "********************",
        "AWS_ACCESS_SECRET_KEY": "dGVIW3yfyY0gy33gI6amS5UyxpS3se8O8ebzdjne",
        "AWS_REGION": "ap-east-1"
      }
    }
  ]
}
```

## Utils cli to use
https://docs.nestjs.com/cli/overview

### Create a new app
```bash
# dry run
npx nest generate app <new app name> -d

# create new app
npx nest generate app <new app name>
```

### create a new module
```bash
# dry run
npx nest generate module <new module name> -p <app name> -d

# create new app
npx nest generate module <new module name> -p <app name>
```

### create a new controller
```bash
# dry run
npx nest generate controller <new controller name> -p <app name> -d

# create new app
npx nest generate controller <new controller name> -p <app name>
```

### creat a new service
```bash
# dry run
npx nest generate service <new service name> -p <app name> -d

# create new app
npx nest generate service <new service name> -p <app name>
```

### create a new class
```bash
# dry run
npx nest generate class <new class name> -p <app name> -d

# create new app
npx nest generate class <new class name> -p <app name>
```

## Local Development (Outdated)
1. Start Kafka service
   1. Install Docker environment, recommend installing Docker Desktop application
   2. Run `docker-compose -f ./dev_scripts/kafka-compose.yml up -d`
2. After installing dependencies, you can start the local service through VSCode Run And Debug feature (for development debugging), using the launch.json configuration shown above
   1. DGW local service default port is 3000
   2. Configure web-hybrid project with `"realAgentServicesBaseUrl": "http://localhost:3000/"`

## Test Deployment (Outdated)
1. Switch to hk22: /var/www/realagent-services
2. Switch to the branch to be deployed (develop)
3. Run `docker build -t housesigma/realagent-services:{tag} .` Note: tag example `20250402-cyh-01`, just make sure it's unique
4. Switch to hk22: /var/www/docker-compose
5. Edit the `docker-compose.yaml` file, modify the image name: `image: housesigma/realagent-services:20250402-cyh-01`
6. Run `docker compose up -d realagent-services`


## Run tests

```bash
# unit tests
$ pnpm run test

# e2e tests
$ pnpm run test:e2e

# test coverage
$ pnpm run test:cov
```
