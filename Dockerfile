FROM node:22.15.0-alpine3.21 AS base

RUN npm i -g pnpm@10.10.0 pm2@6.0.5
RUN pm2 install pm2-logrotate && pm2 set pm2-logrotate:max_size 100M

FROM base AS dependencies

WORKDIR /app
COPY package.json pnpm-lock.yaml ./
RUN pnpm install

FROM base AS build

WORKDIR /app
COPY . .
COPY --from=dependencies /app/node_modules ./node_modules
RUN pnpm build
RUN pnpm prune --prod

FROM base AS deploy

WORKDIR /app
COPY *.config.js ./
COPY --from=build /app/dist/ ./dist/
COPY --from=build /app/node_modules ./node_modules

ENTRYPOINT ["sh", "-c", "pm2-runtime start $APP_NAME.config.js --env $NODE_ENV"]
