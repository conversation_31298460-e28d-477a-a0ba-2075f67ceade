import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { request } from 'undici';

@Injectable()
export class WorkflowService {
  private readonly logger = new Logger(WorkflowService.name);

  constructor(private readonly configService: ConfigService) {}

  async callWorkflow(inputs: { [key: string]: string }, agentKey: string = 'agent1'): Promise<string> {
    const response = await request(`${this.configService.get('agents.difyWorkflowUrl')}`, {
      method: 'POST',
      headers: {
        'content-type': 'application/json',
        authorization: `Bearer ${this.configService.get(`agents.${agentKey}.accessToken`)}`
      },
      body: JSON.stringify({
        inputs,
        response_mode: 'streaming',
        user: `${this.configService.get('agents.difyUser')}`
      })
    });
    return await response.body.text();
  }
}
