import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { WorkflowService } from './workflow.service';
import { ChatSessionService } from './chat-session.service';
import { LiveChatService } from './livechat.service';

@Module({
  imports: [ConfigModule],
  providers: [WorkflowService, ChatSessionService, LiveChatService],
  exports: [WorkflowService, ChatSessionService, LiveChatService]
})
export class CommonSharedModule {}
