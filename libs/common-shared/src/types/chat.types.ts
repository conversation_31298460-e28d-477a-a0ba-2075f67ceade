import { User } from './user.types';

enum ChatIntention {
  default = 'default',
  other = 'other',
  unlockAccount = 'unlockAccount', // unlock user account
  recommListing = 'recommListing', // recommend listings
  takeDownListing = 'takeDownListing' // take down a listing
}

interface ChatHistory {
  id: string;
  userId: string; // FK to User
  totalSessions: number;
  lastActivityAt: Date;
  createdAt: Date;
  updatedAt: Date;

  // Relationships
  user?: User; // 1:1
  sessions: ChatSession[]; // 1:N
}

interface ChatSession {
  id: string;
  chatHistoryId: string; // FK to ChatHistory
  customerId: string; // FK to User (customer)
  agentId?: string; // FK to User (agent) - nullable for unassigned
  status: 'waiting' | 'active' | 'closed' | 'transferred';
  startedAt: Date;
  endedAt?: Date;
  duration?: number; // in seconds
  tags: string[];

  // Relationships
  threads: ChatThread[]; // 1:N
}

interface ChatThread {
  id: string;
  sessionId: string; // FK to ChatSession
  intention?: ChatIntention; // nullable for unknown intention
  status: 'active' | 'resolved' | 'pending';
  pendingEventId?: string;
  createdAt: Date;
  resolvedAt?: Date;

  // Relationships
  session?: ChatSession; // N:1
  events: ChatEvent[]; // 1:N
}

interface ChatEvent {
  id: string;
  threadId: string; // FK to ChatThread
  senderId: string; // FK to User
  type: 'message' | 'join' | 'leave';
  content?: string;
  timestamp: Date;

  // Relationships
  thread?: ChatThread; // N:1
  sender?: User; // N:1
}

interface ChatMessagePayload {
  eventId: string;
  threadId: string;
  chatId: string;
  senderId: string;
  text: string;
  eventType: 'message' | 'system_message';
  systemMessageType?: string;
  timestamp: number; // Unix epoch time to avoid type loss
}

export { ChatEvent, ChatHistory, ChatIntention, ChatSession, ChatThread, ChatMessagePayload };
