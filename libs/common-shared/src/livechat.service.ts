import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { request } from 'undici';

interface LiveChatUser {
  id: string;
  name: string;
  email?: string;
  present: boolean;
  type: string;
}

interface LiveChat {
  id: string;
  users: LiveChatUser[];
}

@Injectable()
export class LiveChatService {
  private readonly logger = new Logger(LiveChatService.name);
  private readonly baseUrl = 'https://api.livechatinc.com/v3.5';

  constructor(private readonly configService: ConfigService) {}

  private async makeRequest<T>(method: string, endpoint: string, body?: object): Promise<T> {
    const bearerToken = this.configService.get(`liveChat.accessToken`, '');
    const { statusCode, body: responseBody } = await request(`${this.baseUrl}${endpoint}`, {
      method,
      headers: {
        Authorization: `Basic ${bearerToken}`,
        'Content-Type': 'application/json'
      },
      body: body ? JSON.stringify(body) : undefined
    });

    if (statusCode !== 200) {
      throw new Error(`API request failed with status ${statusCode}`);
    }

    return JSON.parse(await responseBody.text());
  }

  async processChat(chatId: string, msg: string): Promise<void> {
    try {
      const agentUserId = this.configService.get(`liveChat.agentUserId`, '');

      // 1. Get chat details
      this.logger.log(`Getting chat details for ID: ${chatId}`);
      const chatResponse = await this.makeRequest<LiveChat>('POST', `/agent/action/get_chat`, { chat_id: chatId });

      // 2. Check if agent user exists
      const hasAgentJoined = chatResponse.users.some((user) => user.id === agentUserId && user.present);
      this.logger.log(`Agent has joined: ${hasAgentJoined}`);

      // 3. Add user if not exists
      if (!hasAgentJoined) {
        this.logger.log('Adding agent to chat...');
        await this.makeRequest('POST', '/agent/action/add_user_to_chat', {
          chat_id: chatId,
          user_id: agentUserId,
          user_type: 'agent',
          visibility: 'all',
          ignore_requester_presence: true
        });
        this.logger.log('Agent added successfully');
      }

      // 4. Send message
      this.logger.log('Sending message...');
      await this.makeRequest('POST', '/agent/action/send_event', {
        chat_id: chatId,
        event: {
          type: 'message',
          text: msg,
          visibility: 'all'
        }
      });
      this.logger.log('Message sent successfully');
    } catch (error) {
      this.logger.error('Error processing chat:', error);
      throw error;
    }
  }
}
