import { Injectable } from '@nestjs/common';
import { ChatEvent, ChatIntention, ChatThread } from './types/chat.types';

@Injectable()
export class ChatSessionService {
  private readonly threads: ChatThread[];

  constructor() {
    this.threads = [];
  }

  async fetchActiveThread(threadId: string): Promise<ChatThread | undefined> {
    return this.threads.find((item) => item.id === threadId && item.status !== 'resolved');
  }

  async createThenReturnThread(threadId: string, sessionId: string) {
    const thread = await this.fetchActiveThread(threadId);
    if (thread === undefined) {
      const newThread: ChatThread = {
        id: threadId,
        sessionId,
        status: 'active',
        createdAt: new Date(),
        events: []
      };
      this.threads.push(newThread);
      return newThread;
    }
    return thread;
  }

  async addEventToThread(threadId: string, event: ChatEvent) {
    const thread = await this.fetchActiveThread(threadId);
    if (thread !== undefined) {
      thread.events.push(event);
    }
  }

  async checkDuplicateEvent(threadId: string, chatEvent: ChatEvent): Promise<boolean> {
    let isDuplicate: boolean = false;
    this.threads.map((thread) => {
      if (thread.id === threadId) {
        if (thread.events.findIndex((event) => event.id === chatEvent.id) > -1) {
          isDuplicate = true;
        }
      }
    });
    return isDuplicate;
  }

  async updateThreadIntention(threadId: string, intention: ChatIntention) {
    const thread = await this.fetchActiveThread(threadId);
    if (thread !== undefined && thread.intention === undefined) {
      thread.intention = intention;
    }
  }

  async closeThread(threadId: string) {
    for (let i = this.threads.length - 1; i >= 0; i--) {
      if (this.threads[i].id === threadId && this.threads[i].status !== 'resolved') {
        this.threads[i].status = 'resolved';
      }
    }
  }

  async setPendingEventLock(threadId: string, eventId: string) {
    const thread = await this.fetchActiveThread(threadId);
    if (thread !== undefined && thread.pendingEventId === undefined) {
      thread.pendingEventId = eventId;
      thread.status = 'pending';
    }
  }

  async releasePendingEventLock(threadId: string) {
    const thread = await this.fetchActiveThread(threadId);
    if (thread !== undefined && thread.pendingEventId !== undefined) {
      delete thread.pendingEventId;
      thread.status = 'active';
    }
  }
}
