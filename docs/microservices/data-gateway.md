# Data Gateway Microservice

## Overview
The Data Gateway microservice is a hybrid service that combines HTTP API functionality with Kafka-based data processing. It serves as the primary data ingestion and processing hub, featuring clustered worker architecture for high throughput and AWS S3 integration for data storage and analytics.

## Key Features
- **Hybrid Architecture**: Combined HTTP API server and Kafka microservice
- **Clustered Workers**: Multi-process worker architecture for scalability
- **Data Collection & Storage**: User action tracking and data aggregation
- **Cloud Storage Integration**: AWS S3 integration for data persistence
- **Analytics Support**: Parquet file format support for data analytics
- **CORS Support**: Configurable cross-origin request handling

## Technology Stack
- **Runtime**: Node.js with TypeScript
- **Framework**: NestJS with Fastify adapter
- **HTTP Server**: Fastify for high-performance API endpoints
- **Message Processing**: Apache Kafka via KafkaJS
- **Cloud Storage**: AWS S3 SDK
- **Data Format**: Parquet files for analytics (@dsnp/parquetjs)
- **Clustering**: Node.js cluster module for multi-process architecture
- **Validation**: class-validator, class-transformer
- **Real IP Detection**: nestjs-real-ip

## Architecture
```
                                                            
   HTTP Clients        Kafka Producers        AWS S3        
         ,                    ,                     ,       
                                                       
          �                      �                       �
                                                                 
                    Data Gateway Service                         
                                                                 $
  Primary Process (API Server)       Worker Processes           
    DataGatewayController            KafkaConsumerService     
    UserActionController             File Processing          
    CORS & Validation                S3 Upload Service        
                                                                 
```

### Core Components

#### API Server (Primary Process)
- **DataGatewayController**: Core data collection endpoints
- **UserActionController**: User action tracking and analytics
- **Health Check**: Service status monitoring

#### Worker Services (Worker Processes)
- **KafkaConsumerService**: Processes incoming data events from Kafka
- **KafkaConsumerController**: Routes Kafka messages to appropriate handlers

#### Shared Services
- **AWSS3Service**: Handles file uploads to AWS S3 with organized folder structure
- **FileService**: File system operations and management
- **ParquetService**: Parquet file creation and manipulation for analytics

#### Decorators & Utilities
- **AccessTokenDecorator**: Token-based authentication handling
- **HeadersDecorator**: HTTP header extraction and processing
- **CollectExceptionFilter**: Custom exception handling for data collection

### Data Processing Pipeline
1. **Data Ingestion**: HTTP endpoints collect user actions and data
2. **Validation**: Input validation using DTOs and class-validator
3. **Queue Processing**: Events sent to Kafka for async processing
4. **File Processing**: Worker processes handle file operations
5. **Cloud Storage**: Processed data uploaded to AWS S3
6. **Analytics**: Data stored in Parquet format for analytics workflows

## Configuration

### Clustering
- **Worker Management**: Automatic worker spawning based on CPU cores
- **Worker Limits**: Configurable maximum worker count
- **Process Monitoring**: Automatic worker restart on failure
- **Load Balancing**: Built-in load distribution across workers

### Storage Configuration
- **AWS S3**: Configurable bucket, region, and credentials
- **File Organization**: Date-based folder structure (YYYY/MM/DD)
- **Parquet Support**: Analytics-optimized data storage format

### API Configuration
- **Port**: 3000 (HTTP server)
- **CORS**: Configurable cross-origin settings
- **Versioning**: URI-based API versioning
- **Validation**: Global validation pipes

## API Endpoints
- **Health Check**: Service status and worker information
- **Data Collection**: `/collect` - General data ingestion endpoint
- **User Actions**: `/user-action` - User behavior tracking
- **Data Gateway**: Core data processing endpoints

## Features

### High Availability
- Multi-process clustering for fault tolerance
- Automatic worker recovery and spawning
- Health monitoring and status reporting

### Data Analytics
- Parquet file format support for efficient analytics
- S3 integration for cloud-scale data storage
- Organized data structure for time-series analysis

### Scalability
- Horizontal scaling through clustering
- Kafka-based async processing
- Worker process isolation

## Deployment
- **Container Support**: Docker containerization ready
- **Environment Configuration**: Comprehensive environment variable support
- **Cloud Integration**: AWS S3 integration for production deployments
- **Monitoring**: Built-in health checks and worker status monitoring