# MCP Tools Microservice

## Overview
The MCP Tools microservice is a specialized HTTP API service that provides Model Context Protocol (MCP) server implementations for various external tools and services. It acts as a bridge between AI/LLM applications and external APIs, offering standardized tool interfaces for Google Maps, user account management, and general-purpose utilities.

## Key Features
- **MCP Server Implementation**: Full Model Context Protocol server compliance
- **Multi-Tool Support**: Integration with multiple external service APIs
- **Standardized Tool Interface**: Consistent API patterns across all tools
- **Schema Validation**: Zod-based input validation with JSON schema generation
- **High Performance**: Fastify-based HTTP server for optimal performance
- **Comprehensive Logging**: Detailed request/response logging and error handling

## Technology Stack
- **Runtime**: Node.js with TypeScript  
- **Framework**: NestJS with Fastify adapter
- **MCP Protocol**: Model Context Protocol SDK (@modelcontextprotocol/sdk)
- **Schema Validation**: Zod with zod-to-json-schema
- **HTTP Client**: Undici for optimized HTTP requests
- **API Versioning**: URI-based versioning support
- **External APIs**: Google Maps, user management systems

## Architecture
```
AI/LLM Clients → MCP Protocol → Tool Controllers → External APIs
                     ↓
              Schema Validation
                     ↓
              Service Implementations
```

### Core Components

#### MCP Server Framework
- **Server Setup**: Model Context Protocol compliant server implementation
- **Tool Registration**: Dynamic tool discovery and registration system
- **Request Handling**: Standardized request/response processing
- **Error Management**: Comprehensive error handling and reporting

#### Tool Modules

##### Google Maps Integration (`google-maps`)
- **Geocoding**: Address to coordinates conversion
- **Reverse Geocoding**: Coordinates to address resolution  
- **Place Search**: Location-based place discovery
- **Place Details**: Detailed information about specific locations
- **Distance Matrix**: Multi-point distance and travel time calculations
- **Elevation**: Elevation data for geographic coordinates
- **Directions**: Turn-by-turn navigation routing

**Available Tools:**
- `maps_geocode` - Convert address to coordinates
- `maps_reverse_geocode` - Convert coordinates to address
- `maps_search_places` - Search for places by query
- `maps_place_details` - Get detailed place information
- `maps_distance_matrix` - Calculate distances and travel times
- `maps_elevation` - Get elevation data
- `maps_directions` - Get turn-by-turn directions

##### User Account Management (`user-account`)
- **Account Operations**: User account management and operations
- **Authentication Support**: User authentication and session management
- **Profile Management**: User profile data handling

##### Everything Tool (`everything`)
- **General Purpose**: Flexible tool for various operations
- **Multi-function Support**: Configurable for different use cases
- **Extensible Design**: Easy to extend for new requirements

### Tool Interface Standards

#### Input Schema Validation
- **Zod Schemas**: Type-safe input validation
- **JSON Schema Generation**: Automatic OpenAPI-compatible schemas
- **Error Handling**: Detailed validation error reporting

#### Response Format
- **Standardized Structure**: Consistent response format across all tools
- **Content Types**: Support for text, JSON, and structured data
- **Error Reporting**: Standardized error response format

## Configuration
- **Port**: 3002 (HTTP server)
- **Host**: 0.0.0.0 (all interfaces)
- **API Keys**: External service API key management
- **Validation**: Global input validation
- **Logging**: Comprehensive request/response logging

## External Service Integrations

### Google Maps Platform
- **Maps API**: Full Google Maps Platform API access
- **Authentication**: API key-based authentication
- **Rate Limiting**: Built-in rate limiting compliance
- **Error Handling**: Google Maps specific error handling

## MCP Protocol Compliance
- **Tool Discovery**: `/list_tools` endpoint for tool enumeration
- **Tool Execution**: `/call_tool` endpoint for tool execution  
- **Schema Definition**: Complete input/output schema definitions
- **Error Standards**: MCP-compliant error response format

## API Endpoints
- **Tool Listing**: GET `/tools` - List all available tools
- **Tool Execution**: POST `/tools/:name` - Execute specific tool
- **Health Check**: GET `/health` - Service status monitoring
- **Schema**: GET `/schema/:tool` - Get tool input schema

## Features

### Developer Experience
- **Type Safety**: Full TypeScript support with strict typing
- **Schema Validation**: Runtime input validation with helpful error messages
- **Documentation**: Auto-generated API documentation from schemas
- **Testing**: Comprehensive test coverage for all tools

### Extensibility
- **Modular Design**: Easy addition of new tool modules
- **Plugin Architecture**: Support for custom tool implementations
- **Configuration Management**: Environment-based configuration

### Reliability
- **Error Handling**: Robust error handling and recovery
- **Logging**: Detailed logging for debugging and monitoring
- **Health Monitoring**: Built-in health check endpoints

## Deployment
- **Container Ready**: Docker containerization support
- **Environment Configuration**: Comprehensive environment variable support
- **API Key Management**: Secure external API key handling
- **Health Monitoring**: Built-in health checks and service monitoring
