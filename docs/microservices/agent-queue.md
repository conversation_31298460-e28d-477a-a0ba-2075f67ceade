# Agent Queue Microservice

## Overview
The Agent Queue microservice is a background job processing service that consumes messages from Apache Kafka and executes various data processing tasks. It's designed as a pure microservice without HTTP endpoints, focusing exclusively on message-driven processing.

## Key Features
- **Kafka Message Processing**: Consumes job messages from Kafka queues
- **Multi-job Type Support**: Handles different types of processing jobs
- **Progress Tracking**: Built-in progress tracking utilities
- **Robust Error Handling**: Kafka-specific exception filters
- **Validation**: Input validation using class-validator

## Technology Stack
- **Runtime**: Node.js with TypeScript
- **Framework**: NestJS Microservice
- **Message Queue**: Apache Kafka via KafkaJS
- **Transport**: Kafka Transport Protocol
- **Validation**: class-validator, class-transformer
- **Testing**: Jest with e2e testing support

## Architecture
```
Kafka Topics -> KafkaWorkerController -> KafkaWorkerService -> Specific Job Services
```

### Core Components

#### Controllers
- **KafkaWorkerController**: Handles incoming Kafka messages and routes to appropriate service

#### Services
- **KafkaWorkerService**: Main orchestrator that processes job messages based on `jobKey`
- **ParseLotDimensionService**: (experiment, deprecated) Specialized service for parsing lot dimension data
- **ParseFreshdeskReviewsService**: (experiment, deprecated) Handles processing of Freshdesk review files

#### Utilities
- **ProgressTracker**: (to be replaced) Utility for tracking job progress
- **KafkaMessageExceptionFilter**: Custom exception handling for Kafka operations

### Supported Job Types
- `parseLotDimension`: (experiment, deprecated) Processes lot dimension parsing requests
- `parseFreshDeskReview`: (experiment, deprecated) Handles Freshdesk review file processing

## Configuration
- Kafka broker configuration (connection options, topics, consumer groups)
- Environment-specific settings via ConfigService
- Global validation pipes for message validation

## Deployment
- Containerized deployment support
- Scalable worker instances
- Health monitoring and logging

## Message Format
```typescript
interface JobMessage {
  jobKey: 'parseLotDimension' | 'parseFreshDeskReview';
  payload: {
    // job-specific payload structure
  };
}
```

## Port Configuration
- **Service Type**: Microservice (no HTTP port)
- **Transport**: Kafka messaging protocol
