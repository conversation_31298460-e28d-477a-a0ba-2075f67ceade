# Agent RESTful Microservice

## Overview
The Agent RESTful microservice is a high-performance HTTP API service built with Fastify and NestJS. It provides RESTful endpoints for real estate data processing, live chat webhook handling, and various parsing services. The service integrates with external AI/LLM services and live chat platforms.

## Key Features
- **High-Performance HTTP Server**: Built on Fastify for optimal performance
- **Live Chat Integration**: Webhook handling for LiveChat platform integration
- **AI/LLM Integration**: Uses MCP (Model Context Protocol) clients for AI services
- **Data Processing**: Address and parking data parsing capabilities
- **Real-time Communication**: WebSocket support for live interactions
- **API Versioning**: URI-based versioning support

## Technology Stack
- **Runtime**: Node.js with TypeScript
- **Framework**: NestJS with Fastify adapter
- **HTTP Server**: Fastify (high-performance alternative to Express)
- **AI Integration**: Model Context Protocol (MCP) SDK, Anthropic SDK
- **Validation**: class-validator, class-transformer
- **Real IP Detection**: nestjs-real-ip
- **User Agent Parsing**: ua-parser-js
- **Testing**: Jest with comprehensive test coverage

## Architecture
```
HTTP Requests -> Fastify -> NestJS Controllers -> Services -> External APIs/MCP Clients
```

### Core Components

#### Controllers & Modules
- **HomeController**: Health checks and service status endpoints
- **ParseAddressController**: (experiment, deprecated) Address parsing and validation services
- **ParseParkingController**: (experiment, deprecated) Parking information extraction services  
- **WebhookLivechatController**: LiveChat webhook event processing

#### Services
- **WebhookLivechatService**: Handles LiveChat events, manages chat sessions, integrates with AI workflows
- **ParseAddressService**: (experiment, deprecated) Address parsing and geocoding services
- **ParseParkingService**: (experiment, deprecated) Parking data extraction and processing

#### Utilities
- **McpClient**: Model Context Protocol client for AI service integration
- **ChatSessionMcpClient**: (to be replaced) Specialized MCP client for chat session management
- **LiveChatAgent**: Client for LiveChat platform API interactions

#### Types & Interfaces
- **ChatTypes**: Chat session, event, and intention type definitions
- **LiveChatTypesLite**: (to be replaced) LiveChat platform event type definitions
- **User**: User management type definitions

### Key Integrations

#### LiveChat Platform
- Real-time webhook processing
- Chat session management
- Agent assignment and message routing
- Thread lifecycle management (create, update, close)

#### AI/LLM Services
- Workflow integration for intelligent responses
- Intent classification and routing
- Context-aware conversation handling
- Multi-turn dialogue support

## API Endpoints
- **Health Check**: Service status and availability
- **Address Parsing**: (experiment, deprecated) `/parse-address` - Address validation and parsing
- **Parking Parsing**: (experiment, deprecated) `/parse-parking` - Parking information extraction
- **LiveChat Webhooks**: `/webhook-livechat` - Real-time chat event processing

## Configuration
- **Port**: 3001 (HTTP server)
- **Host**: 0.0.0.0 (all interfaces)
- **API Versioning**: URI-based versioning enabled
- **Logging**: Fastify native logging enabled
- **Validation**: Global validation pipes enabled

## Features

### Chat Session Management
- Duplicate event detection and deduplication
- Thread state management (active, pending, closed)
- Event locking to prevent race conditions
- Intent classification and workflow routing

### Data Processing
- Address normalization and validation
- Parking information extraction
- Real estate data parsing and enrichment

### External Service Integration
- LiveChat platform API integration
- AI workflow service integration
- Real-time event processing and response generation

## Deployment
- **Container Ready**: Dockerfile support
- **Environment Configuration**: Environment-based configuration
- **Health Monitoring**: Built-in health check endpoints
- **Logging**: Structured logging with request tracking
