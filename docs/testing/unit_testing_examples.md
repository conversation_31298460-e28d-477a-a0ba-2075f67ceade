# NestJS Unit Testing Best Practices

## Table of Contents
- [Advanced Testing Scenarios](#advanced-testing-scenarios)
- [Best Practices](#best-practices)

## Advanced Testing Scenarios

### Testing with Configuration Service

```typescript
// config-dependent.service.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { PaymentService } from './payment.service';

describe('PaymentService', () => {
  let service: PaymentService;
  let configService: jest.Mocked<ConfigService>;

  beforeEach(async () => {
    // Arrange: Mock configuration service
    const mockConfigService = {
      get: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PaymentService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<PaymentService>(PaymentService);
    configService = module.get(ConfigService);
  });

  it('should use correct API key from config', async () => {
    // Arrange
    const mockApiKey = 'test-api-key';
    configService.get.mockReturnValue(mockApiKey);

    // Act
    const apiKey = service.getApiKey();

    // Assert
    expect(configService.get).toHaveBeenCalledWith('PAYMENT_API_KEY');
    expect(apiKey).toBe(mockApiKey);
  });
});
```

### Testing with Custom Decorators

```typescript
// custom-decorator.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { RolesGuard } from './roles.guard';

describe('RolesGuard', () => {
  let guard: RolesGuard;
  let reflector: jest.Mocked<Reflector>;

  beforeEach(async () => {
    // Arrange
    const mockReflector = {
      getAllAndOverride: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RolesGuard,
        {
          provide: Reflector,
          useValue: mockReflector,
        },
      ],
    }).compile();

    guard = module.get<RolesGuard>(RolesGuard);
    reflector = module.get(Reflector);
  });

  it('should allow access when user has required role', () => {
    // Arrange
    const requiredRoles = ['admin'];
    const mockUser = { roles: ['admin', 'user'] };
    const mockContext = {
      getHandler: jest.fn(),
      getClass: jest.fn(),
      switchToHttp: jest.fn(() => ({
        getRequest: jest.fn(() => ({ user: mockUser })),
      })),
    } as unknown as ExecutionContext;

    reflector.getAllAndOverride.mockReturnValue(requiredRoles);

    // Act
    const result = guard.canActivate(mockContext);

    // Assert
    expect(result).toBe(true);
    expect(reflector.getAllAndOverride).toHaveBeenCalledWith('roles', [
      mockContext.getHandler(),
      mockContext.getClass(),
    ]);
  });
});
```

### Testing with Database Transactions

```typescript
// transaction.service.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { DataSource, QueryRunner } from 'typeorm';
import { TransactionService } from './transaction.service';

describe('TransactionService', () => {
  let service: TransactionService;
  let dataSource: jest.Mocked<DataSource>;
  let queryRunner: jest.Mocked<QueryRunner>;

  beforeEach(async () => {
    // Arrange: Mock DataSource and QueryRunner
    queryRunner = {
      connect: jest.fn(),
      startTransaction: jest.fn(),
      commitTransaction: jest.fn(),
      rollbackTransaction: jest.fn(),
      release: jest.fn(),
      manager: {
        save: jest.fn(),
      },
    } as any;

    const mockDataSource = {
      createQueryRunner: jest.fn().mockReturnValue(queryRunner),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TransactionService,
        {
          provide: DataSource,
          useValue: mockDataSource,
        },
      ],
    }).compile();

    service = module.get<TransactionService>(TransactionService);
    dataSource = module.get(DataSource);
  });

  describe('executeInTransaction', () => {
    it('should commit transaction on success', async () => {
      // Arrange
      const mockData = { id: 1, name: 'Test' };
      (queryRunner.manager.save as jest.Mock).mockResolvedValue(mockData);

      // Act
      const result = await service.executeInTransaction(mockData);

      // Assert
      expect(dataSource.createQueryRunner).toHaveBeenCalled();
      expect(queryRunner.connect).toHaveBeenCalled();
      expect(queryRunner.startTransaction).toHaveBeenCalled();
      expect(queryRunner.commitTransaction).toHaveBeenCalled();
      expect(queryRunner.release).toHaveBeenCalled();
      expect(result).toEqual(mockData);
    });

    it('should rollback transaction on error', async () => {
      // Arrange
      const error = new Error('Database error');
      (queryRunner.manager.save as jest.Mock).mockRejectedValue(error);

      // Act & Assert
      await expect(service.executeInTransaction({})).rejects.toThrow(error);
      expect(queryRunner.rollbackTransaction).toHaveBeenCalled();
      expect(queryRunner.release).toHaveBeenCalled();
    });
  });
});
```

### Testing with Event Emitters

```typescript
// event-driven.service.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { UserService } from './user.service';
import { UserCreatedEvent } from './events/user-created.event';

describe('UserService with Events', () => {
  let service: UserService;
  let eventEmitter: jest.Mocked<EventEmitter2>;

  beforeEach(async () => {
    // Arrange
    const mockEventEmitter = {
      emit: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserService,
        {
          provide: EventEmitter2,
          useValue: mockEventEmitter,
        },
      ],
    }).compile();

    service = module.get<UserService>(UserService);
    eventEmitter = module.get(EventEmitter2);
  });

  it('should emit event when user is created', async () => {
    // Arrange
    const createUserDto = { name: 'John', email: '<EMAIL>' };
    const mockUser = { id: 1, ...createUserDto };

    // Act
    const result = await service.createUser(createUserDto);

    // Assert
    expect(eventEmitter.emit).toHaveBeenCalledWith(
      'user.created',
      new UserCreatedEvent(mockUser),
    );
  });
});
```

## Best Practices

### 1. Test Structure and Organization

```typescript
describe('ComponentName', () => {
  // Group related tests
  describe('method name', () => {
    describe('when condition', () => {
      it('should expected behavior', () => {
        // Arrange
        // Act
        // Assert
      });
    });
  });
});
```

### 2. Mock Management

```typescript
// Use factory functions for consistent mocks
const createMockUser = (overrides: Partial<User> = {}): User => ({
  id: 1,
  name: 'John Doe',
  email: '<EMAIL>',
  createdAt: new Date(),
  updatedAt: new Date(),
  ...overrides,
});

// Clear mocks between tests
afterEach(() => {
  jest.clearAllMocks();
});
```

### 3. Assertion Best Practices

```typescript
// Be specific with assertions
expect(service.create).toHaveBeenCalledWith(expectedData);
expect(service.create).toHaveBeenCalledTimes(1);

// Test both positive and negative scenarios
it('should handle valid input', () => { /* ... */ });
it('should throw error for invalid input', () => { /* ... */ });

// Use toEqual for objects, toBe for primitives
expect(result).toEqual(expectedObject);
expect(result.id).toBe(1);
```

### 4. Test Data Management

```typescript
// Use builders for complex test data
class UserBuilder {
  private user: Partial<User> = {};

  withId(id: number): UserBuilder {
    this.user.id = id;
    return this;
  }

  withEmail(email: string): UserBuilder {
    this.user.email = email;
    return this;
  }

  build(): User {
    return { ...defaultUser, ...this.user };
  }
}

// Usage in tests
const user = new UserBuilder()
  .withId(1)
  .withEmail('<EMAIL>')
  .build();
```

### 5. Error Testing Patterns

```typescript
// Testing async errors
await expect(service.methodThatThrows()).rejects.toThrow('Expected error');
await expect(service.methodThatThrows()).rejects.toThrow(NotFoundException);

// Testing sync errors
expect(() => service.syncMethodThatThrows()).toThrow('Expected error');
```

### 6. Performance and Maintainability

```typescript
// Use test utilities for common patterns
const setupTestModule = async (providers: any[]) => {
  return Test.createTestingModule({
    providers,
  }).compile();
};

// Extract common test data
const testData = {
  validUser: { name: 'John', email: '<EMAIL>' },
  invalidUser: { name: '', email: 'invalid' },
};
```
