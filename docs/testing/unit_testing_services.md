## Testing Setup

### Basic Test Module Configuration

```typescript
import { Test, TestingModule } from '@nestjs/testing';

describe('ComponentName', () => {
  let component: ComponentType;
  let mockDependency: jest.Mocked<DependencyType>;

  beforeEach(async () => {
    // Arrange: Setup test module
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ControllerClass],
      providers: [
        {
          provide: DependencyClass,
          useValue: createMockDependency(),
        },
      ],
    }).compile();

    component = module.get<ComponentType>(ComponentClass);
    mockDependency = module.get(DependencyClass);
  });

  // Test cases here...
});
```

### Mock Factory Helper

```typescript
// test/helpers/mock-factory.ts
export const createMockRepository = () => ({
  find: jest.fn(),
  findOne: jest.fn(),
  findOneBy: jest.fn(),
  save: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  create: jest.fn(),
});

export const createMockService = <T>(methods: string[]): jest.Mocked<T> => {
  const mock = {} as any;
  methods.forEach(method => {
    mock[method] = jest.fn();
  });
  return mock;
};
```

## Service Testing

### Basic Service with Repository Pattern

```typescript
// users.service.ts
@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  async findAll(): Promise<User[]> {
    return this.userRepository.find();
  }

  async findOne(id: number): Promise<User> {
    const user = await this.userRepository.findOneBy({ id });
    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }
    return user;
  }

  async create(createUserDto: CreateUserDto): Promise<User> {
    const user = this.userRepository.create(createUserDto);
    return this.userRepository.save(user);
  }

  async update(id: number, updateUserDto: UpdateUserDto): Promise<User> {
    const user = await this.findOne(id);
    Object.assign(user, updateUserDto);
    return this.userRepository.save(user);
  }

  async remove(id: number): Promise<void> {
    const user = await this.findOne(id);
    await this.userRepository.delete(id);
  }
}
```

### Service Test Suite

```typescript
// users.service.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NotFoundException } from '@nestjs/common';
import { UsersService } from './users.service';
import { User } from './entities/user.entity';
import { CreateUserDto, UpdateUserDto } from './dto';

describe('UsersService', () => {
  let service: UsersService;
  let repository: jest.Mocked<Repository<User>>;

  const mockUser: User = {
    id: 1,
    name: 'John Doe',
    email: '<EMAIL>',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    // Arrange: Setup test module with repository mock
    const mockRepository = {
      find: jest.fn(),
      findOneBy: jest.fn(),
      create: jest.fn(),
      save: jest.fn(),
      delete: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: getRepositoryToken(User),
          useValue: mockRepository,
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
    repository = module.get(getRepositoryToken(User));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('findAll', () => {
    it('should return all users', async () => {
      // Arrange
      const expectedUsers = [mockUser];
      repository.find.mockResolvedValue(expectedUsers);

      // Act
      const result = await service.findAll();

      // Assert
      expect(result).toEqual(expectedUsers);
      expect(repository.find).toHaveBeenCalledTimes(1);
    });

    it('should return empty array when no users exist', async () => {
      // Arrange
      repository.find.mockResolvedValue([]);

      // Act
      const result = await service.findAll();

      // Assert
      expect(result).toEqual([]);
      expect(repository.find).toHaveBeenCalledTimes(1);
    });
  });

  describe('findOne', () => {
    it('should return a user when found', async () => {
      // Arrange
      const userId = 1;
      repository.findOneBy.mockResolvedValue(mockUser);

      // Act
      const result = await service.findOne(userId);

      // Assert
      expect(result).toEqual(mockUser);
      expect(repository.findOneBy).toHaveBeenCalledWith({ id: userId });
    });

    it('should throw NotFoundException when user not found', async () => {
      // Arrange
      const userId = 999;
      repository.findOneBy.mockResolvedValue(null);

      // Act & Assert
      await expect(service.findOne(userId)).rejects.toThrow(
        new NotFoundException(`User with ID ${userId} not found`),
      );
      expect(repository.findOneBy).toHaveBeenCalledWith({ id: userId });
    });
  });

  describe('create', () => {
    it('should create and return a new user', async () => {
      // Arrange
      const createUserDto: CreateUserDto = {
        name: 'John Doe',
        email: '<EMAIL>',
      };
      repository.create.mockReturnValue(mockUser as any);
      repository.save.mockResolvedValue(mockUser);

      // Act
      const result = await service.create(createUserDto);

      // Assert
      expect(result).toEqual(mockUser);
      expect(repository.create).toHaveBeenCalledWith(createUserDto);
      expect(repository.save).toHaveBeenCalledWith(mockUser);
    });
  });

  describe('update', () => {
    it('should update and return the user', async () => {
      // Arrange
      const userId = 1;
      const updateUserDto: UpdateUserDto = { name: 'Jane Doe' };
      const updatedUser = { ...mockUser, name: 'Jane Doe' };

      repository.findOneBy.mockResolvedValue(mockUser);
      repository.save.mockResolvedValue(updatedUser);

      // Act
      const result = await service.update(userId, updateUserDto);

      // Assert
      expect(result).toEqual(updatedUser);
      expect(repository.findOneBy).toHaveBeenCalledWith({ id: userId });
      expect(repository.save).toHaveBeenCalled();
    });

    it('should throw NotFoundException when user to update not found', async () => {
      // Arrange
      const userId = 999;
      const updateUserDto: UpdateUserDto = { name: 'Jane Doe' };
      repository.findOneBy.mockResolvedValue(null);

      // Act & Assert
      await expect(service.update(userId, updateUserDto)).rejects.toThrow(
        new NotFoundException(`User with ID ${userId} not found`),
      );
    });
  });

  describe('remove', () => {
    it('should remove a user', async () => {
      // Arrange
      const userId = 1;
      repository.findOneBy.mockResolvedValue(mockUser);
      repository.delete.mockResolvedValue({ affected: 1 } as any);

      // Act
      await service.remove(userId);

      // Assert
      expect(repository.findOneBy).toHaveBeenCalledWith({ id: userId });
      expect(repository.delete).toHaveBeenCalledWith(userId);
    });

    it('should throw NotFoundException when user to remove not found', async () => {
      // Arrange
      const userId = 999;
      repository.findOneBy.mockResolvedValue(null);

      // Act & Assert
      await expect(service.remove(userId)).rejects.toThrow(
        new NotFoundException(`User with ID ${userId} not found`),
      );
    });
  });
});
```

### Testing Service Dependencies

```typescript
// notification.service.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { NotificationService } from './notification.service';
import { EmailService } from '../email/email.service';
import { UsersService } from '../users/users.service';

describe('NotificationService', () => {
  let service: NotificationService;
  let emailService: jest.Mocked<EmailService>;
  let usersService: jest.Mocked<UsersService>;

  beforeEach(async () => {
    // Arrange: Setup service with multiple dependencies
    const mockEmailService = {
      sendEmail: jest.fn(),
    };

    const mockUsersService = {
      findOne: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NotificationService,
        {
          provide: EmailService,
          useValue: mockEmailService,
        },
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
      ],
    }).compile();

    service = module.get<NotificationService>(NotificationService);
    emailService = module.get(EmailService);
    usersService = module.get(UsersService);
  });

  describe('sendWelcomeEmail', () => {
    it('should send welcome email to user', async () => {
      // Arrange
      const userId = 1;
      const mockUser = { id: 1, email: '<EMAIL>', name: 'John' };
      usersService.findOne.mockResolvedValue(mockUser);
      emailService.sendEmail.mockResolvedValue(true);

      // Act
      await service.sendWelcomeEmail(userId);

      // Assert
      expect(usersService.findOne).toHaveBeenCalledWith(userId);
      expect(emailService.sendEmail).toHaveBeenCalledWith({
        to: mockUser.email,
        subject: 'Welcome!',
        template: 'welcome',
        context: { name: mockUser.name },
      });
    });

    it('should handle email sending failure', async () => {
      // Arrange
      const userId = 1;
      const mockUser = { id: 1, email: '<EMAIL>', name: 'John' };
      usersService.findOne.mockResolvedValue(mockUser);
      emailService.sendEmail.mockRejectedValue(new Error('Email service unavailable'));

      // Act & Assert
      await expect(service.sendWelcomeEmail(userId)).rejects.toThrow('Email service unavailable');
    });
  });
});
```

This comprehensive guide covers the essential patterns for unit testing NestJS applications with Jest. Remember to:

- Always follow the AAA pattern
- Keep tests isolated and independent
- Mock external dependencies properly
- Test both success and failure scenarios
- Cover edge cases and error conditions
- Use descriptive test names
- Maintain good test coverage without over-testing implementation details
