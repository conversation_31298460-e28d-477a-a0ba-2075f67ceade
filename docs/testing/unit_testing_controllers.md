## Testing Setup

### Basic Test Module Configuration

```typescript
import { Test, TestingModule } from '@nestjs/testing';

describe('ComponentName', () => {
  let component: ComponentType;
  let mockDependency: jest.Mocked<DependencyType>;

  beforeEach(async () => {
    // Arrange: Setup test module
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ControllerClass],
      providers: [
        {
          provide: DependencyClass,
          useValue: createMockDependency(),
        },
      ],
    }).compile();

    component = module.get<ComponentType>(ComponentClass);
    mockDependency = module.get(DependencyClass);
  });

  // Test cases here...
});
```

### Mock Factory Helper

```typescript
// test/helpers/mock-factory.ts
export const createMockRepository = () => ({
  find: jest.fn(),
  findOne: jest.fn(),
  findOneBy: jest.fn(),
  save: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
  create: jest.fn(),
});

export const createMockService = <T>(methods: string[]): jest.Mocked<T> => {
  const mock = {} as any;
  methods.forEach(method => {
    mock[method] = jest.fn();
  });
  return mock;
};
```

## Controller Testing

### Basic Controller with CRUD Operations

```typescript
// users.controller.ts
@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get()
  async findAll(): Promise<User[]> {
    return this.usersService.findAll();
  }

  @Get(':id')
  async findOne(@Param('id') id: string): Promise<User> {
    return this.usersService.findOne(+id);
  }

  @Post()
  async create(@Body() createUserDto: CreateUserDto): Promise<User> {
    return this.usersService.create(createUserDto);
  }

  @Put(':id')
  async update(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto,
  ): Promise<User> {
    return this.usersService.update(+id, updateUserDto);
  }

  @Delete(':id')
  async remove(@Param('id') id: string): Promise<void> {
    return this.usersService.remove(+id);
  }
}
```

### Controller Test Suite

```typescript
// users.controller.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { CreateUserDto, UpdateUserDto } from './dto';
import { User } from './entities/user.entity';

describe('UsersController', () => {
  let controller: UsersController;
  let service: jest.Mocked<UsersService>;

  const mockUser: User = {
    id: 1,
    name: 'John Doe',
    email: '<EMAIL>',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockUsers: User[] = [mockUser];

  beforeEach(async () => {
    // Arrange: Setup test module
    const mockUsersService = {
      findAll: jest.fn(),
      findOne: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      remove: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [UsersController],
      providers: [
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
      ],
    }).compile();

    controller = module.get<UsersController>(UsersController);
    service = module.get(UsersService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('findAll', () => {
    it('should return an array of users', async () => {
      // Arrange
      service.findAll.mockResolvedValue(mockUsers);

      // Act
      const result = await controller.findAll();

      // Assert
      expect(result).toEqual(mockUsers);
      expect(service.findAll).toHaveBeenCalledTimes(1);
      expect(service.findAll).toHaveBeenCalledWith();
    });

    it('should handle empty results', async () => {
      // Arrange
      service.findAll.mockResolvedValue([]);

      // Act
      const result = await controller.findAll();

      // Assert
      expect(result).toEqual([]);
      expect(service.findAll).toHaveBeenCalledTimes(1);
    });
  });

  describe('findOne', () => {
    it('should return a single user', async () => {
      // Arrange
      const userId = '1';
      service.findOne.mockResolvedValue(mockUser);

      // Act
      const result = await controller.findOne(userId);

      // Assert
      expect(result).toEqual(mockUser);
      expect(service.findOne).toHaveBeenCalledWith(1);
      expect(service.findOne).toHaveBeenCalledTimes(1);
    });

    it('should handle user not found', async () => {
      // Arrange
      const userId = '999';
      service.findOne.mockRejectedValue(new Error('User not found'));

      // Act & Assert
      await expect(controller.findOne(userId)).rejects.toThrow('User not found');
      expect(service.findOne).toHaveBeenCalledWith(999);
    });
  });

  describe('create', () => {
    it('should create a new user', async () => {
      // Arrange
      const createUserDto: CreateUserDto = {
        name: 'John Doe',
        email: '<EMAIL>',
      };
      service.create.mockResolvedValue(mockUser);

      // Act
      const result = await controller.create(createUserDto);

      // Assert
      expect(result).toEqual(mockUser);
      expect(service.create).toHaveBeenCalledWith(createUserDto);
      expect(service.create).toHaveBeenCalledTimes(1);
    });
  });

  describe('update', () => {
    it('should update an existing user', async () => {
      // Arrange
      const userId = '1';
      const updateUserDto: UpdateUserDto = { name: 'Jane Doe' };
      const updatedUser = { ...mockUser, name: 'Jane Doe' };
      service.update.mockResolvedValue(updatedUser);

      // Act
      const result = await controller.update(userId, updateUserDto);

      // Assert
      expect(result).toEqual(updatedUser);
      expect(service.update).toHaveBeenCalledWith(1, updateUserDto);
      expect(service.update).toHaveBeenCalledTimes(1);
    });
  });

  describe('remove', () => {
    it('should delete a user', async () => {
      // Arrange
      const userId = '1';
      service.remove.mockResolvedValue(undefined);

      // Act
      await controller.remove(userId);

      // Assert
      expect(service.remove).toHaveBeenCalledWith(1);
      expect(service.remove).toHaveBeenCalledTimes(1);
    });
  });
});
```

### Testing Controllers with Guards and Interceptors

```typescript
// auth.controller.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { JwtAuthGuard } from '../guards/jwt-auth.guard';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';

describe('AuthController with Guards', () => {
  let controller: AuthController;
  let service: jest.Mocked<AuthService>;

  beforeEach(async () => {
    // Arrange: Setup test module with guard
    const mockAuthService = {
      login: jest.fn(),
      validateUser: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuthController],
      providers: [
        {
          provide: AuthService,
          useValue: mockAuthService,
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({ canActivate: jest.fn(() => true) })
      .compile();

    controller = module.get<AuthController>(AuthController);
    service = module.get(AuthService);
  });

  it('should handle protected route', async () => {
    // Arrange
    const mockRequest = { user: { id: 1, email: '<EMAIL>' } };
    
    // Act
    const result = await controller.getProfile(mockRequest);

    // Assert
    expect(result).toEqual(mockRequest.user);
  });
});
```

This comprehensive guide covers the essential patterns for unit testing NestJS applications with Jest. Remember to:

- Always follow the AAA pattern
- Keep tests isolated and independent
- Mock external dependencies properly
- Test both success and failure scenarios
- Cover edge cases and error conditions
- Use descriptive test names
- Maintain good test coverage without over-testing implementation details
