{"name": "realagent-services", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"apps/**/*.ts\" \"libs/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/apps/data-gateway/main.js", "start:aq": "nest start agent-queue", "start:aq:dev": "nest start agent-queue --watch", "start:aq:debug": "nest start agent-queue --debug --watch", "start:ar": "nest start agent-restful", "start:ar:dev": "nest start agent-restful --watch", "start:ar:debug": "nest start agent-restful --debug --watch", "start:mt": "nest start mcp-tools", "start:mt:dev": "nest start mcp-tools --watch", "start:mt:debug": "nest start mcp-tools --debug --watch", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./apps/data-gateway/test/jest-e2e.json"}, "dependencies": {"@anthropic-ai/sdk": "^0.41.0", "@aws-sdk/client-s3": "^3.637.0", "@dsnp/parquetjs": "^1.8.3", "@modelcontextprotocol/sdk": "^1.16.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.2.3", "@nestjs/core": "^10.0.0", "@nestjs/microservices": "^10.4.1", "@nestjs/platform-fastify": "^10.4.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "kafkajs": "^2.2.4", "nestjs-real-ip": "^3.0.1", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "ua-parser-js": "^1.0.38", "undici": "^7.8.0", "zod": "^3.24.4", "zod-to-json-schema": "^3.24.5"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/jest": "^29.5.2", "@types/node": "^22.15.17", "@types/request-ip": "^0.0.41", "@types/supertest": "^6.0.0", "@types/ua-parser-js": "^0.7.39", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "fastify": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["<rootDir>/{apps,libs}/**/*.ts", "!**/test/**"], "coverageDirectory": "./coverage", "testEnvironment": "node", "roots": ["<rootDir>/apps/", "<rootDir>/libs/"], "moduleNameMapper": {"^@app/common-shared(|/.*)$": "<rootDir>/libs/common-shared/src/$1"}, "coverageThreshold": {"global": {"branches": 50, "functions": 50, "lines": 50, "statements": 50}}}, "packageManager": "pnpm@9.15.3+sha512.1f79bc245a66eb0b07c5d4d83131240774642caaa86ef7d0434ab47c0d16f66b04e21e0c086eb61e62c77efc4d7f7ec071afad3796af64892fae66509173893a"}