name: Build Container

on:
  workflow_dispatch:
    inputs:
      tag:
        description: 'New tag name'
        required: true
  # push:
  #   tags:
  #   - 'v[0-9]+\.[0-9]+\.[0-9]+'
  
jobs:
  build:
    name: Build Docker Image
    runs-on: [ self-hosted, linux, X64, CA, OVH ]
    steps:  
        - name: clean workspace before starting job
          run: find ${{ github.workspace }} -mindepth 1 -delete

        - uses: actions/checkout@692973e3d937129bcbf40652eb9f2f61becf3332 # v4.1.7
          
        - name: Setup AWS ECR Details
          uses: aws-actions/configure-aws-credentials@v4
          with:
            aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
            aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
            aws-region: ca-central-1

        - name: Login to Amazon ECR
          id: login-pf-aws-ecr
          uses: aws-actions/amazon-ecr-login@v2

        - name: Build and push the tagged docker image to Amazon ECR
          env:
            ECR_REGISTRY: ${{ steps.login-pf-aws-ecr.outputs.registry }}
            ECR_REPOSITORY: housesigma/realagent-services
            IMAGE_TAG: ${{ github.ref_name }}
          run: |
            docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG .
            docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG

        - name: Setup kubectl 
          uses: azure/setup-kubectl@v4
          with:
            version: 'v1.30.3' 
          id: install


        - name: Update kube config
          run: aws eks update-kubeconfig --name hs-front-eks-cluster

        - name: Deploy to EKS
          env:
            ECR_REGISTRY: ${{ steps.login-pf-aws-ecr.outputs.registry }}
            ECR_REPOSITORY: housesigma/realagent-services
            IMAGE_TAG: ${{ github.ref_name }}
          run: |
            kubectl set image deployment/user-data-collection user-data-collection=$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG --namespace default


  notify:
    needs: [ build ]
    runs-on: [ self-hosted, linux, X64, CA, OVH ]
    steps:
    - name: clean workspace before starting job
      run: find ${{ github.workspace }} -mindepth 1 -delete

    - uses: actions/checkout@692973e3d937129bcbf40652eb9f2f61becf3332 # v4.1.7
      with:
        fetch-depth: 0

    - name: Format commit logs
      run: |
        tags=$(git tag --sort=-creatordate | head -n 2)
        
        if [ $(echo "$tags" | wc -l) -lt 2 ]; then
            echo "Not enough tags in the repository"
            exit 1
        fi
        
        new_tag=$(echo "$tags" | sed -n '1p')
        old_tag=$(echo "$tags" | sed -n '2p')
        
        echo "Comparing between tags: $new_tag and $old_tag"
        
        commits=$(git log --pretty=format:"%s" "$old_tag".."$new_tag")

        TASKS=$(echo "$commits" | grep -i "Merge pull request" | grep -oP '([DEV]+-[0-9]+)' | jq -c -Rn '[inputs | split("\n") | .[] | select(length > 0)]') || true

        echo JSON_BODY=$(echo "{\"repo\":\"$GITHUB_REPOSITORY\",\"version\":\"$GITHUB_REF_NAME\",\"tasks\":$TASKS}" | jq -c) >> $GITHUB_ENV

    - name: Send WebHook
      uses: fjogeleit/http-request-action@44816be1eabb9c1122d8d775923f39bbe55c67a3 # v1.16.1
      with:
        url: "${{ secrets.RELEASE_WEBHOOK_HOST }}/webhook/github"
        method: 'POST'
        bearerToken: ${{ secrets.RELEASE_WEBHOOK_TOKEN }}
        data: ${{ env.JSON_BODY }}
        timeout: 30000
