import { SSEServerTransport } from '@modelcontextprotocol/sdk/server/sse.js';
import { StreamableHTTPServerTransport } from '@modelcontextprotocol/sdk/server/streamableHttp.js';
import { Body, Controller, Get, Headers, HttpCode, Logger, Post, Query, Req, Res } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { FastifyReply, FastifyRequest } from 'fastify';
import { buildNotAllowedResponse } from '../../../utils/common';
import { createServer } from './user-account';

@Controller('tools/user_account')
export class UserAccountController {
  private readonly logger = new Logger(UserAccountController.name);
  private readonly mcpServer;
  private readonly transports: {
    sse: { [key: string]: SSEServerTransport };
    streamable: { [key: string]: StreamableHTTPServerTransport };
  } = {
    sse: {},
    streamable: {}
  };

  constructor(private readonly configService: ConfigService) {
    this.mcpServer = createServer(this.configService.get('tools.userAccount.authSessionId') || '');
  }

  @Get('')
  @HttpCode(405)
  index() {
    return buildNotAllowedResponse();
  }

  @Get('mcp')
  @HttpCode(405)
  mcp() {
    return buildNotAllowedResponse();
  }

  @Post('mcp')
  async streamable(
    @Req() req: FastifyRequest,
    @Res() res: FastifyReply,
    @Body() body: any,
    @Headers('mcp-session-id') sessionId?: string
  ) {
    this.logger.log(`Session id: ${sessionId}`);

    const transport: StreamableHTTPServerTransport = new StreamableHTTPServerTransport({
      sessionIdGenerator: undefined
    });

    res.raw.on('close', () => {
      transport.close();
      this.mcpServer.close();
    });

    if (req.headers.accept !== `"accept":"application/json, text/event-stream"`) {
      req.headers.accept = `"accept":"application/json, text/event-stream"`;
    }

    await this.mcpServer.connect(transport);
    await transport.handleRequest(req.raw, res.raw, body);
  }

  @Get('sse')
  async sse(@Res() res: FastifyReply) {
    const transport = new SSEServerTransport(`tools/user_account/message`, res.raw);
    this.transports.sse[transport.sessionId] = transport;

    this.mcpServer.onclose = async () => {
      await this.mcpServer.close();
    };

    await this.mcpServer.connect(transport);
  }

  @Post('message')
  async sendSseMessage(
    @Query('sessionId') sessionId: string,
    @Req() req: FastifyRequest,
    @Res() res: FastifyReply,
    @Body() body: any
  ) {
    if (!this.transports.sse[sessionId]) {
      return buildNotAllowedResponse();
    }
    await this.transports.sse[sessionId].handlePostMessage(req.raw, res.raw, body);
  }
}
