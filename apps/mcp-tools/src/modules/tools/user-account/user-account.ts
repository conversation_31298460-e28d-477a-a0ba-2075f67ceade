import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { CallToolRequestSchema, ListToolsRequestSchema, Tool, ToolSchema } from '@modelcontextprotocol/sdk/types.js';
import { request } from 'undici';
import { z } from 'zod';
import { zodToJsonSchema } from 'zod-to-json-schema';

const ToolInputSchema = ToolSchema.shape.inputSchema;
type ToolInput = z.infer<typeof ToolInputSchema>;

const UnlockUserAccountSchema = z.object({
  userName: z.string().describe('Username of the account to unlock'),
  email: z.string().email().describe('Email address of the user'),
  phone: z.string().describe('Phone number of the user'),
  remarks: z.string().optional().describe('Optional remarks for the unlock operation')
});

enum ToolName {
  UNLOCK_USER_ACCOUNT = 'unlock_user_account'
}

async function unlockUserAccountAPI(
  email: string,
  phone: string,
  authSessionId: string
): Promise<{ success: boolean; message: string }> {
  try {
    // First, fetch user ID using email
    const { statusCode: searchStatusCode, body: searchBody } = await request(
      'https://hkdev.internal.housesigma.com/v3/manage_user_account/searchUser',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Cookie: `PHPSESSID=${authSessionId}`
        },
        body: JSON.stringify({
          searchKey: email
        })
      }
    );

    const searchResponseText = await searchBody.text();

    if (searchStatusCode < 200 || searchStatusCode >= 300) {
      return {
        success: false,
        message: `Failed to search user: HTTP ${searchStatusCode}`
      };
    }

    let searchData;
    try {
      searchData = JSON.parse(searchResponseText);
    } catch {
      return {
        success: false,
        message: 'Failed to parse search user response'
      };
    }

    // Check if the response indicates success (error code 0) and extract user_id
    if (searchData.error?.code !== 0) {
      return {
        success: false,
        message: `User search failed: ${searchData.error?.message || 'Unknown error'}`
      };
    }

    const userId = searchData.data?.user_id;
    if (!userId) {
      return {
        success: false,
        message: 'User ID not found in search response'
      };
    }

    // Now unlock the user account using the fetched user ID
    const { statusCode, body } = await request(
      'https://hkdev.internal.housesigma.com/v3/manage_consumer_status/update',
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Cookie: `PHPSESSID=${authSessionId}`
        },
        body: JSON.stringify({
          block: false,
          user_id: userId
        })
      }
    );

    const responseText = await body.text();

    if (statusCode >= 200 && statusCode < 300) {
      let data;
      try {
        data = JSON.parse(responseText);
      } catch {
        data = { message: 'Account unlocked successfully' };
      }

      return {
        success: true,
        message: data.message || 'Account unlocked successfully'
      };
    } else {
      return {
        success: false,
        message: `Failed to unlock account: HTTP ${statusCode}`
      };
    }
  } catch (error) {
    return {
      success: false,
      message: `Error unlocking account: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

export const createServer = (authSessionId: string) => {
  const server = new Server(
    {
      name: 'user-account-server',
      version: '1.0.0'
    },
    {
      capabilities: {
        tools: {}
      }
    }
  );

  server.setRequestHandler(ListToolsRequestSchema, async () => {
    const tools: Tool[] = [
      {
        name: ToolName.UNLOCK_USER_ACCOUNT,
        description: 'Unlocks a user account with email and phone',
        inputSchema: zodToJsonSchema(UnlockUserAccountSchema) as ToolInput
      }
    ];

    return { tools };
  });

  server.setRequestHandler(CallToolRequestSchema, async (request) => {
    const { name, arguments: args } = request.params;

    if (name === ToolName.UNLOCK_USER_ACCOUNT) {
      const validatedArgs = UnlockUserAccountSchema.parse(args);
      const { userName, email, phone, remarks } = validatedArgs;

      const result = await unlockUserAccountAPI(email, phone, authSessionId);

      const responseText = result.success
        ? `User account unlock successful for ${userName} (${email}). ${result.message}${remarks ? ` Remarks: ${remarks}` : ''}`
        : `Failed to unlock account for ${userName} (${email}). ${result.message}${remarks ? ` Remarks: ${remarks}` : ''}`;

      return {
        content: [
          {
            type: 'text',
            text: responseText
          }
        ],
        isError: !result.success
      };
    }

    throw new Error(`Unknown tool: ${name}`);
  });

  return server;
};
