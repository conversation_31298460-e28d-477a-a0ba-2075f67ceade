import { Logger, ValidationPipe, VersioningType } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { FastifyAdapter, NestFastifyApplication } from '@nestjs/platform-fastify';
import { McpToolsModule } from './mcp-tools/mcp-tools.module';

const logger = new Logger('Bootstrap');

const bootstrap = async () => {
  const app = await NestFactory.create<NestFastifyApplication>(
    McpToolsModule,
    new FastifyAdapter({
      logger: true
    })
  );

  app.useGlobalPipes(new ValidationPipe());
  app.enableVersioning({
    type: VersioningType.URI
  });
  await app.listen(3002, '0.0.0.0');
};

bootstrap()
  .then(async () => {
    logger.log(`Primary app running on ${process.pid}`);
  })
  .catch((e) => {
    logger.error(e);
    process.exit(1);
  });
