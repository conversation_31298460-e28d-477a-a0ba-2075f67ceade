import { CommonSharedModule } from '@app/common-shared';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import configuration from '../config/configuration';
import { HomeModule } from '../modules/home/<USER>';
import { ToolsModule } from '../modules/tools/tools.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      cache: true,
      load: [configuration]
    }),
    CommonSharedModule,
    HomeModule,
    ToolsModule
  ]
})
export class McpToolsModule {}
