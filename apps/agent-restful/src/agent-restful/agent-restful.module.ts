import { CommonSharedModule } from '@app/common-shared';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import configuration from '../config/configuration';
import { HomeModule } from '../modules/home/<USER>';
import { ParseAddressModule } from '../modules/parse-address/parse-address.module';
import { ParseParkingModule } from '../modules/parse-parking/parse-parking.module';
import { WebhookLivechatModule } from '../modules/webhook-livechat/webhook-livechat.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      cache: true,
      load: [configuration]
    }),
    CommonSharedModule,
    HomeModule,
    ParseAddressModule,
    ParseParkingModule,
    WebhookLivechatModule
  ]
})
export class AgentRestfulModule {}
