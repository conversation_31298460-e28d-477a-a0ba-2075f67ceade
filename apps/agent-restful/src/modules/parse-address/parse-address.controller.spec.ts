import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { ParseAddressController } from './parse-address.controller';
import { ParseAddressService } from './parse-address.service';

describe('ParseAddressController', () => {
  let controller: ParseAddressController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ParseAddressController],
      providers: [ParseAddressService, ConfigService]
    }).compile();

    controller = module.get<ParseAddressController>(ParseAddressController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
