import Anthropic from '@anthropic-ai/sdk';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { MCPClient } from '../../utils/mcp-client';

@Injectable()
export class ParseAddressService {
  constructor(private readonly configService: ConfigService) {}

  private readonly logger: Logger = new Logger(ParseAddressService.name);

  private readonly anthropic = new Anthropic({
    apiKey: this.configService.get<string>(`llm.Claude.apiKey`)
  });

  async callWithoutLLM(payload: string) {
    const client = new Client({ name: 'mcp-client', version: '1.0.0' });
    const mcpClient = new MCPClient(this.anthropic, client);

    await mcpClient.connectToServer(`http://localhost:3002/tools/google-maps/mcp`);
    const { content } = await mcpClient.callToolDirectly('maps_geocode', { address: `${payload}` });
    await mcpClient.cleanup();
    return `${JSON.stringify(content, null, 2)}`;
  }

  async callWithLLM(payload: string) {
    const client = new Client({ name: 'mcp-client', version: '1.0.0' });
    const mcpClient = new MCPClient(this.anthropic, client);

    await mcpClient.connectToServer(`http://localhost:3002/tools/google-maps/mcp`);
    const prompt = `Find the geo coordinates of the address: ${payload}`;
    const content = await mcpClient.processQuery(prompt);
    await mcpClient.cleanup();
    return `${content}`;
  }
}
