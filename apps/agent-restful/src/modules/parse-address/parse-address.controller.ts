import { Body, Controller, Logger, Post, Version } from '@nestjs/common';
import { ParseAddressService } from './parse-address.service';

@Controller('parse-address')
export class ParseAddressController {
  constructor(private readonly parseAddressService: ParseAddressService) {}
  private readonly logger: Logger = new Logger(ParseAddressController.name);

  @Version('1')
  @Post('no-llm')
  async parseAddress(@Body() body: { value: string }) {
    const result = await this.parseAddressService.callWithoutLLM(body.value);
    return `Parsed: ${result}`;
  }

  @Version('1')
  @Post('llm')
  async parseWithLLM(@Body() body: { value: string }) {
    const result = await this.parseAddressService.callWithLLM(body.value);
    return `Final text: ${result}`;
  }
}
