import { CommonSharedModule } from '@app/common-shared';
import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { WebhookLivechatController } from './webhook-livechat.controller';
import { WebhookLivechatService } from './webhook-livechat.service';

@Module({
  imports: [
    CommonSharedModule,
    ClientsModule.registerAsync([
      {
        name: 'KAFKA_SERVICE',
        useFactory: async (configService: ConfigService) => ({
          transport: Transport.KAFKA,
          options: configService.get('kafka.options')
        }),
        inject: [ConfigService]
      }
    ])
  ],
  controllers: [WebhookLivechatController],
  providers: [WebhookLivechatService]
})
export class WebhookLivechatModule {}
