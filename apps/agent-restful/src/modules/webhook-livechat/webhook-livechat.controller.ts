import { Body, Controller, Post, Version } from '@nestjs/common';
import { WebhookLivechatService } from './webhook-livechat.service';

@Controller('webhook/livechat')
export class WebhookLivechatController {
  constructor(private readonly webhookLivechatService: WebhookLivechatService) {}

  @Version('1')
  @Post('events')
  async handleWebhook(@Body() body: { [key: string]: unknown }) {
    // webhook verification key: 6Ua4cncBUYfxGedVlPSeG2VKTN2ABowE
    await this.webhookLivechatService.parseEvent(body);
    return { message: `Webhook received` };
  }
}
