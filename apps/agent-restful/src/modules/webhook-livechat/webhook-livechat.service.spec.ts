import { ConfigService } from '@nestjs/config';
import { ClientKafka } from '@nestjs/microservices';
import { Test, TestingModule } from '@nestjs/testing';
import { of } from 'rxjs';
import { IncomingEvent } from '@app/common-shared/types/livechat.types';
import { WebhookLivechatService } from './webhook-livechat.service';

describe('WebhookLivechatService', () => {
  let service: WebhookLivechatService;
  let configService: jest.Mocked<ConfigService>;
  let kafkaClient: jest.Mocked<ClientKafka>;

  const mockAgentUserId = 'agent-123';

  const createMockIncomingEvent = (overrides: Partial<IncomingEvent['payload']> = {}): IncomingEvent => ({
    webhook_id: 'webhook-123',
    secret_key: 'secret-123',
    action: 'incoming_event',
    payload: {
      chat_id: 'chat-123',
      thread_id: 'thread-123',
      event: {
        id: 'event-123',
        custom_id: 'custom-123',
        visibility: 'all',
        created_at: new Date(),
        author_id: 'user-123',
        type: 'message',
        text: 'Hello, I need help'
      },
      ...overrides
    }
  });

  beforeEach(async () => {
    const mockConfigService = {
      get: jest.fn()
    };

    const mockKafkaClient = {
      connect: jest.fn(),
      emit: jest.fn()
    };

    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WebhookLivechatService,
        {
          provide: ConfigService,
          useValue: mockConfigService
        },
        {
          provide: 'KAFKA_SERVICE',
          useValue: mockKafkaClient
        }
      ]
    }).compile();

    service = module.get<WebhookLivechatService>(WebhookLivechatService);
    configService = module.get(ConfigService) as jest.Mocked<ConfigService>;
    kafkaClient = module.get('KAFKA_SERVICE') as jest.Mocked<ClientKafka>;

    configService.get.mockImplementation((key: string, defaultValue?: unknown) => {
      if (key === 'liveChat.agentUserId') return mockAgentUserId;
      if (key === 'kafka.topicLiveChat') return 'live-chat-topic';
      return defaultValue;
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('parseEvent', () => {
    describe('when receiving a message event', () => {
      it('should publish message to Kafka topic', async () => {
        const incomingEvent = createMockIncomingEvent();
        kafkaClient.emit.mockReturnValue(of(undefined));

        await service.parseEvent(incomingEvent);

        expect(kafkaClient.emit).toHaveBeenCalledWith('live-chat-topic', {
          eventId: 'event-123',
          threadId: 'thread-123',
          chatId: 'chat-123',
          senderId: 'user-123',
          text: 'Hello, I need help',
          eventType: 'message',
          systemMessageType: undefined,
          timestamp: expect.any(Number)
        });
      });

      it('should handle Kafka emit errors', async () => {
        const incomingEvent = createMockIncomingEvent();
        kafkaClient.emit.mockImplementation(() => {
          throw new Error('Kafka error');
        });

        await expect(service.parseEvent(incomingEvent)).rejects.toThrow('Kafka error');
      });

      it('should publish agent messages to Kafka', async () => {
        const incomingEvent = createMockIncomingEvent({
          event: {
            ...createMockIncomingEvent().payload.event,
            author_id: mockAgentUserId
          }
        });
        kafkaClient.emit.mockReturnValue(of(undefined));

        await service.parseEvent(incomingEvent);

        expect(kafkaClient.emit).toHaveBeenCalledWith(
          'live-chat-topic',
          expect.objectContaining({
            senderId: mockAgentUserId
          })
        );
      });
    });

    describe('when receiving a system message event', () => {
      it('should publish system message to Kafka topic', async () => {
        const incomingEvent = createMockIncomingEvent({
          event: {
            ...createMockIncomingEvent().payload.event,
            type: 'system_message',
            system_message_type: 'routing.archived_inactive'
          }
        });

        kafkaClient.emit.mockReturnValue(of(undefined));

        await service.parseEvent(incomingEvent);

        expect(kafkaClient.emit).toHaveBeenCalledWith('live-chat-topic', {
          eventId: 'event-123',
          threadId: 'thread-123',
          chatId: 'chat-123',
          senderId: 'user-123',
          text: 'Hello, I need help',
          eventType: 'system_message',
          systemMessageType: 'routing.archived_inactive',
          timestamp: expect.any(Number)
        });
      });
    });

    describe('when receiving non-message events', () => {
      it('should handle invalid payloads gracefully', async () => {
        const invalidPayload = { invalid: 'payload' };

        await service.parseEvent(invalidPayload);

        expect(kafkaClient.emit).not.toHaveBeenCalled();
      });

      it('should handle unhandled event types', async () => {
        const incomingEvent = createMockIncomingEvent({
          event: {
            ...createMockIncomingEvent().payload.event,
            type: 'file' as any
          }
        });

        await service.parseEvent(incomingEvent);

        expect(kafkaClient.emit).not.toHaveBeenCalled();
      });
    });
  });
});
