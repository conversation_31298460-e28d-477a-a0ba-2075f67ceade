import { Inject, Injectable, Logger, OnApplicationBootstrap } from '@nestjs/common';
import { ClientKafka } from '@nestjs/microservices';
import { ConfigService } from '@nestjs/config';
import { ChatMessagePayload } from '@app/common-shared/types/chat.types';
import { IncomingEvent, isIncomingEvent } from '@app/common-shared/types/livechat.types';

@Injectable()
export class WebhookLivechatService implements OnApplicationBootstrap {
  private readonly logger = new Logger(WebhookLivechatService.name);

  constructor(
    private readonly configService: ConfigService,
    @Inject('KAFKA_SERVICE') private kafkaClient: ClientKafka
  ) {}

  async onApplicationBootstrap(): Promise<void> {
    await this.kafkaClient.connect();
  }

  async parseEvent(payload: IncomingEvent | unknown) {
    if (isIncomingEvent(payload)) {
      if (payload.payload.event.type === 'message' || payload.payload.event.type === 'system_message') {
        this.logger.log(`Received ${payload.payload.event.type} event: ${payload.payload.event.id}`);

        const chatMessage: ChatMessagePayload = {
          eventId: payload.payload.event.id,
          threadId: payload.payload.thread_id,
          chatId: payload.payload.chat_id,
          senderId: payload.payload.event.author_id,
          text: payload.payload.event.text,
          eventType: payload.payload.event.type,
          systemMessageType: payload.payload.event.system_message_type,
          timestamp: payload.payload.event.created_at.getTime() // Unix epoch time
        };

        try {
          this.kafkaClient.emit(this.configService.get('kafka.topicLiveChat'), chatMessage);
          this.logger.log(`Chat message published to Kafka: ${payload.payload.event.id}`);
        } catch (error) {
          this.logger.error(`Failed to publish chat message to Kafka: ${error}`);
          throw error;
        }
      } else {
        this.logger.log(`Unhandled event type: ${payload.payload.event.type}`);
      }
    } else {
      this.logger.log(`Invalid payload received`);
    }
  }
}
