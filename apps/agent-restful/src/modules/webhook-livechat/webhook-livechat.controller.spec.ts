import { Test, TestingModule } from '@nestjs/testing';
import { WebhookLivechatController } from './webhook-livechat.controller';
import { WebhookLivechatService } from './webhook-livechat.service';

describe('WebhookLivechatController', () => {
  let controller: WebhookLivechatController;
  let service: jest.Mocked<WebhookLivechatService>;

  const createMockWebhookPayload = (overrides: Record<string, unknown> = {}): Record<string, unknown> => ({
    webhook_id: 'webhook-123',
    secret_key: 'secret-key-456',
    action: 'incoming_event',
    payload: {
      chat_id: 'chat-789',
      thread_id: 'thread-abc',
      event: {
        id: 'event-def',
        custom_id: 'custom-ghi',
        visibility: 'all',
        created_at: '2024-01-01T00:00:00.000Z',
        author_id: 'user-jkl',
        type: 'message',
        text: 'Hello, I need help with my account'
      }
    },
    ...overrides
  });

  beforeEach(async () => {
    // Arrange: Setup mock service
    const mockWebhookLivechatService = {
      parseEvent: jest.fn()
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [WebhookLivechatController],
      providers: [
        {
          provide: WebhookLivechatService,
          useValue: mockWebhookLivechatService
        }
      ]
    }).compile();

    controller = module.get<WebhookLivechatController>(WebhookLivechatController);
    service = module.get(WebhookLivechatService) as jest.Mocked<WebhookLivechatService>;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('handleWebhook', () => {
    describe('when receiving a valid webhook payload', () => {
      it('should process the webhook and return success message', async () => {
        // Arrange
        const mockPayload = createMockWebhookPayload();
        service.parseEvent.mockResolvedValue(undefined);

        // Act
        const result = await controller.handleWebhook(mockPayload);

        // Assert
        expect(service.parseEvent).toHaveBeenCalledWith(mockPayload);
        expect(service.parseEvent).toHaveBeenCalledTimes(1);
        expect(result).toEqual({ message: 'Webhook received' });
      });
    });

    describe('when receiving a message event webhook', () => {
      it('should handle message event payload correctly', async () => {
        // Arrange
        const mockPayload = createMockWebhookPayload({
          payload: {
            chat_id: 'chat-message-123',
            thread_id: 'thread-message-456',
            event: {
              id: 'msg-event-789',
              type: 'message',
              text: 'I need assistance with my order',
              author_id: 'customer-abc',
              visibility: 'all',
              created_at: '2024-01-15T10:30:00.000Z'
            }
          }
        });
        service.parseEvent.mockResolvedValue(undefined);

        // Act
        const result = await controller.handleWebhook(mockPayload);

        // Assert
        expect(service.parseEvent).toHaveBeenCalledWith(mockPayload);
        expect(result).toEqual({ message: 'Webhook received' });
      });
    });

    describe('when receiving a system message event webhook', () => {
      it('should handle system message event payload correctly', async () => {
        // Arrange
        const mockPayload = createMockWebhookPayload({
          payload: {
            chat_id: 'chat-system-123',
            thread_id: 'thread-system-456',
            event: {
              id: 'sys-event-789',
              type: 'system_message',
              system_message_type: 'routing.archived_inactive',
              author_id: 'system',
              visibility: 'all',
              created_at: '2024-01-15T11:00:00.000Z'
            }
          }
        });
        service.parseEvent.mockResolvedValue(undefined);

        // Act
        const result = await controller.handleWebhook(mockPayload);

        // Assert
        expect(service.parseEvent).toHaveBeenCalledWith(mockPayload);
        expect(result).toEqual({ message: 'Webhook received' });
      });
    });

    describe('when receiving an empty payload', () => {
      it('should handle empty payload gracefully', async () => {
        // Arrange
        const emptyPayload = {};
        service.parseEvent.mockResolvedValue(undefined);

        // Act
        const result = await controller.handleWebhook(emptyPayload);

        // Assert
        expect(service.parseEvent).toHaveBeenCalledWith(emptyPayload);
        expect(service.parseEvent).toHaveBeenCalledTimes(1);
        expect(result).toEqual({ message: 'Webhook received' });
      });
    });

    describe('when receiving an invalid payload structure', () => {
      it('should pass invalid payload to service and return success message', async () => {
        // Arrange
        const invalidPayload = {
          invalid_field: 'invalid_value',
          another_field: 12345
        };
        service.parseEvent.mockResolvedValue(undefined);

        // Act
        const result = await controller.handleWebhook(invalidPayload);

        // Assert
        expect(service.parseEvent).toHaveBeenCalledWith(invalidPayload);
        expect(result).toEqual({ message: 'Webhook received' });
      });
    });

    describe('when service throws an error', () => {
      it('should propagate service errors', async () => {
        // Arrange
        const mockPayload = createMockWebhookPayload();
        const serviceError = new Error('Service processing failed');
        service.parseEvent.mockRejectedValue(serviceError);

        // Act & Assert
        await expect(controller.handleWebhook(mockPayload)).rejects.toThrow('Service processing failed');
        expect(service.parseEvent).toHaveBeenCalledWith(mockPayload);
        expect(service.parseEvent).toHaveBeenCalledTimes(1);
      });
    });

    describe('when service throws a specific error type', () => {
      it('should handle validation errors from service', async () => {
        // Arrange
        const mockPayload = createMockWebhookPayload();
        const validationError = new TypeError('Invalid webhook payload format');
        service.parseEvent.mockRejectedValue(validationError);

        // Act & Assert
        await expect(controller.handleWebhook(mockPayload)).rejects.toThrow(TypeError);
        await expect(controller.handleWebhook(mockPayload)).rejects.toThrow('Invalid webhook payload format');
        expect(service.parseEvent).toHaveBeenCalledWith(mockPayload);
      });
    });

    describe('when service processing takes time', () => {
      it('should wait for service to complete processing', async () => {
        // Arrange
        const mockPayload = createMockWebhookPayload();
        service.parseEvent.mockImplementation(
          () => new Promise((resolve) => setTimeout(() => resolve(undefined), 100))
        );

        // Act
        const start = Date.now();
        const result = await controller.handleWebhook(mockPayload);
        const duration = Date.now() - start;

        // Assert
        expect(duration).toBeGreaterThanOrEqual(90); // Allow some variance
        expect(service.parseEvent).toHaveBeenCalledWith(mockPayload);
        expect(result).toEqual({ message: 'Webhook received' });
      });
    });

    describe('payload variations', () => {
      it('should handle payload with additional unknown fields', async () => {
        // Arrange
        const mockPayload = createMockWebhookPayload({
          extra_field: 'extra_value',
          nested_object: {
            unknown_property: 'unknown_value'
          }
        });
        service.parseEvent.mockResolvedValue(undefined);

        // Act
        const result = await controller.handleWebhook(mockPayload);

        // Assert
        expect(service.parseEvent).toHaveBeenCalledWith(mockPayload);
        expect(result).toEqual({ message: 'Webhook received' });
      });

      it('should handle payload with null values', async () => {
        // Arrange
        const basePayload = createMockWebhookPayload();
        const mockPayload = {
          ...basePayload,
          nullable_field: null,
          payload: {
            ...(basePayload.payload as Record<string, unknown>),
            optional_field: null
          }
        };
        service.parseEvent.mockResolvedValue(undefined);

        // Act
        const result = await controller.handleWebhook(mockPayload);

        // Assert
        expect(service.parseEvent).toHaveBeenCalledWith(mockPayload);
        expect(result).toEqual({ message: 'Webhook received' });
      });

      it('should handle payload with different data types', async () => {
        // Arrange
        const mockPayload = {
          string_field: 'test',
          number_field: 12345,
          boolean_field: true,
          array_field: ['item1', 'item2'],
          object_field: { nested: 'value' }
        };
        service.parseEvent.mockResolvedValue(undefined);

        // Act
        const result = await controller.handleWebhook(mockPayload);

        // Assert
        expect(service.parseEvent).toHaveBeenCalledWith(mockPayload);
        expect(result).toEqual({ message: 'Webhook received' });
      });
    });

    describe('method behavior', () => {
      it('should always return the same success message format', async () => {
        // Arrange
        const mockPayload1 = createMockWebhookPayload({ test: 'payload1' });
        const mockPayload2 = createMockWebhookPayload({ test: 'payload2' });
        service.parseEvent.mockResolvedValue(undefined);

        // Act
        const result1 = await controller.handleWebhook(mockPayload1);
        const result2 = await controller.handleWebhook(mockPayload2);

        // Assert
        expect(result1).toEqual({ message: 'Webhook received' });
        expect(result2).toEqual({ message: 'Webhook received' });
        expect(result1).toEqual(result2);
      });

      it('should call service method exactly once per invocation', async () => {
        // Arrange
        const mockPayload = createMockWebhookPayload();
        service.parseEvent.mockResolvedValue(undefined);

        // Act
        await controller.handleWebhook(mockPayload);
        await controller.handleWebhook(mockPayload);

        // Assert
        expect(service.parseEvent).toHaveBeenCalledTimes(2);
        expect(service.parseEvent).toHaveBeenNthCalledWith(1, mockPayload);
        expect(service.parseEvent).toHaveBeenNthCalledWith(2, mockPayload);
      });
    });
  });
});
