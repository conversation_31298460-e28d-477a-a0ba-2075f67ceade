import { Body, Controller, Logger, Post, Version } from '@nestjs/common';
import { ParseParkingService } from './parse-parking.service';

@Controller('parse-parking')
export class ParseParkingController {
  constructor(private readonly parseParkingService: ParseParkingService) {}

  private readonly logger: Logger = new Logger(ParseParkingController.name);

  @Version('1')
  @Post()
  async parseParking(@Body() body: { value: string }): Promise<string> {
    const result = await this.parseParkingService.parseInput(`${body.value}`);
    return `Parsed: ${result}`;
  }
}
