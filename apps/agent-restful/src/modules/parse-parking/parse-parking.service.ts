import Anthropic from '@anthropic-ai/sdk';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class ParseParkingService {
  constructor(private readonly configService: ConfigService) {}

  private readonly logger: Logger = new Logger(ParseParkingService.name);

  private readonly anthropic = new Anthropic({
    apiKey: this.configService.get<string>(`llm.Claude.apiKey`)
  });

  async parseInput(payload: string) {
    const message = await this.anthropic.messages.create({
      model: 'claude-3-5-sonnet-20241022',
      // stream: true,
      max_tokens: 1024,
      system: [
        {
          type: 'text',
          text: `You're a professional realtor can answer user questions about real estate market.`
        }
      ],
      messages: [
        {
          role: 'user',
          content: `Take a description of a home below, how many covered parking does it have? Output only a number.
${payload}`
        }
      ]
    });

    this.logger.log(`message: ${JSON.stringify(message.content)}`);

    return JSON.stringify(message.content[0]);
  }
}
