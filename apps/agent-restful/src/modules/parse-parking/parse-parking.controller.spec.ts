import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { ParseParkingController } from './parse-parking.controller';
import { ParseParkingService } from './parse-parking.service';

describe('ParseParkingController', () => {
  let controller: ParseParkingController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ParseParkingController],
      providers: [ParseParkingService, ConfigService]
    }).compile();

    controller = module.get<ParseParkingController>(ParseParkingController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
