import { request } from 'undici';

interface User {
  id: string;
  name: string;
  email?: string;
  present: boolean;
  type: string;
}

interface Chat {
  id: string;
  users: User[];
}

//TODO: test only, move me to mcp
class LiveChatAgent {
  private readonly baseUrl = 'https://api.livechatinc.com/v3.5';
  private readonly bearerToken;
  private readonly agentUserId;

  constructor(token: string, agentUserId: string) {
    this.bearerToken = token;
    this.agentUserId = agentUserId;
  }

  private async makeRequest<T>(method: string, endpoint: string, body?: object): Promise<T> {
    const { statusCode, body: responseBody } = await request(`${this.baseUrl}${endpoint}`, {
      method,
      headers: {
        Authorization: `Basic ${this.bearerToken}`,
        'Content-Type': 'application/json'
      },
      body: body ? JSON.stringify(body) : undefined
    });

    if (statusCode !== 200) {
      throw new Error(`API request failed with status ${statusCode}`);
    }

    return JSON.parse(await responseBody.text());
  }

  async processChat(chatId: string, msg: string): Promise<void> {
    try {
      // 1. Get chat details
      console.log(`Getting chat details for ID: ${chatId}`);
      const chatResponse = await this.makeRequest<Chat>('POST', `/agent/action/get_chat`, { chat_id: chatId });

      // 2. Check if "TEST ISA" user exists
      const hasAgentJoined = chatResponse.users.some((user) => user.id === this.agentUserId && user.present);
      console.log(`User "TEST ISA" has joined: ${hasAgentJoined}`);

      // 3. Add user if not exists
      if (!hasAgentJoined) {
        console.log('Adding agent to chat...');
        await this.makeRequest('POST', '/agent/action/add_user_to_chat', {
          chat_id: chatId,
          user_id: this.agentUserId,
          user_type: 'agent',
          visibility: 'all',
          ignore_requester_presence: true
        });
        console.log('Agent added successfully');
      }

      // 4. Send "agent joined" message
      console.log('Sending message...');
      await this.makeRequest('POST', '/agent/action/send_event', {
        chat_id: chatId,
        event: {
          type: 'message',
          text: msg,
          visibility: 'all'
        }
      });
      console.log('Message sent successfully');
    } catch (error) {
      console.error('Error processing chat:', error);
      throw error;
    }
  }
}

export { LiveChatAgent };
