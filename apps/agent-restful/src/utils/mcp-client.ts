import Anthropic from '@anthropic-ai/sdk';
import { MessageParam, Tool } from '@anthropic-ai/sdk/resources/messages/messages';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StreamableHTTPClientTransport } from '@modelcontextprotocol/sdk/client/streamableHttp.js';

class MCPClient {
  private mcp: Client;
  private anthropic: Anthropic;
  private readonly model: string = 'claude-3-5-sonnet-20241022';
  private transport: StreamableHTTPClientTransport | null = null;
  private tools: Tool[] = [];

  constructor(llm: Anthropic, client: Client) {
    this.anthropic = llm;
    this.mcp = client;
  }

  async connectToServer(serverUrl: string) {
    /**
     * Connect to an MCP server
     */
    try {
      this.transport = new StreamableHTTPClientTransport(new URL(serverUrl), {
        sessionId: undefined
      });
      await this.mcp.connect(this.transport);

      // List available tools
      const toolsResult = await this.mcp.listTools();
      this.tools = toolsResult.tools.map((tool) => {
        return {
          name: tool.name,
          description: tool.description,
          input_schema: tool.inputSchema
        };
      });
      console.log(
        'Connected to server with tools:',
        this.tools.map(({ name }) => name)
      );
    } catch (e) {
      console.log('Failed to connect to MCP server: ', e);
      throw e;
    }
  }

  async callToolDirectly(toolName: string, toolArgs: { [key: string]: unknown }) {
    return await this.mcp.callTool({
      name: toolName,
      arguments: toolArgs
    });
  }

  async processQuery(query: string) {
    /**
     * Process a query using Claude and available tools
     *
     * @param query - The user's input query
     * @returns Processed response as a string
     */
    const messages: MessageParam[] = [
      {
        role: 'user',
        content: query
      }
    ];

    // Initial Claude API call
    const response = await this.anthropic.messages.create({
      model: this.model,
      max_tokens: 1024,
      system: [
        {
          type: 'text',
          text: `You are an AI assistant that can use tools to help complete tasks.`
        }
      ],
      messages,
      tools: this.tools
    });

    // Process response and handle tool calls
    const finalText = [];
    const toolResults = [];

    for (const content of response.content) {
      if (content.type === 'text') {
        finalText.push(content.text);
      } else if (content.type === 'tool_use') {
        // Execute tool call
        const toolName = content.name;
        const toolArgs = content.input as { [x: string]: unknown } | undefined;

        const result = await this.mcp.callTool({
          name: toolName,
          arguments: toolArgs
        });
        toolResults.push(result);
        finalText.push(`[Calling tool ${toolName} with args ${JSON.stringify(toolArgs)}]`);

        // Continue conversation with tool results
        messages.push({
          role: 'user',
          content: result.content as string
        });

        // Get next response from Claude
        const response = await this.anthropic.messages.create({
          model: this.model,
          max_tokens: 1024,
          messages
        });

        finalText.push(response.content[0].type === 'text' ? response.content[0].text : '');
      }
    }

    return finalText.join('\n');
  }

  async cleanup() {
    /**
     * Clean up resources
     */
    if (this.transport !== null) {
      await this.transport.close();
    }
    await this.mcp.close();
  }
}

export { MCPClient };
