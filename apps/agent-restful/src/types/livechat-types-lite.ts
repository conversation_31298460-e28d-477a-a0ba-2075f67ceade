type IncomingEvent = {
  webhook_id: string;
  secret_key: string;
  action: 'incoming_event';
  payload: {
    chat_id: string;
    thread_id: string;
    event: {
      id: string;
      custom_id: string;
      visibility: string;
      created_at: Date;
      author_id: string;
      type: 'system_message' | 'message';
      system_message_type?: 'system_message_type' | 'routing.archived_inactive' | 'routing.idle';
      text: string;
    };
  };
};

function isIncomingEvent(event: IncomingEvent | unknown): event is IncomingEvent {
  return (event as IncomingEvent).action === 'incoming_event';
}

export { IncomingEvent, isIncomingEvent };
