export default () => ({
  common: {
    runtime: {
      podName: process.env.MY_POD_NAME,
      nodeEnv: process.env.NODE_ENV,
      pid: process.pid
    }
  },
  llm: {
    Claude: {
      apiKey: process.env.ANTHROPIC_API_KEY
    }
  },
  kafka: {
    topicLiveChat: 'live-chat-topic',
    options: {
      client: {
        clientId: 'ar',
        brokers: process.env.KAFKA_HOSTS?.split(',') || []
      },
      consumer: {
        groupId: 'ar-service'
      }
    }
  }
});
