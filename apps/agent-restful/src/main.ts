import { Logger, ValidationPipe, VersioningType } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { FastifyAdapter, NestFastifyApplication } from '@nestjs/platform-fastify';
import { AgentRestfulModule } from './agent-restful/agent-restful.module';

const logger = new Logger('Bootstrap');

const bootstrap = async () => {
  const app = await NestFactory.create<NestFastifyApplication>(
    AgentRestfulModule,
    new FastifyAdapter({
      logger: true
    })
  );

  app.useGlobalPipes(new ValidationPipe());
  app.enableVersioning({
    type: VersioningType.URI
  });
  await app.listen(3001, '0.0.0.0');
};

bootstrap()
  .then(async () => {
    logger.log(`Primary app running on ${process.pid}`);
  })
  .catch((e) => {
    logger.error(e);
    process.exit(1);
  });
