export default () => ({
  common: {
    allowAPI: process.env.ENABLE_API === 'true',
    debug: process.env.DEBUG === 'true',
    worker: {
      max: process.env.MAX_WORKERS ? parseInt(process.env.MAX_WORKERS) : 2
    },
    threshold: {
      // 超过指定事件数，触发 parquet 文件上传
      eventCount: process.env.EVENT_COUNT ? parseInt(process.env.EVENT_COUNT) : 200,
      // 超过指定时间限制，触发 parquet 文件上传
      timeout: process.env.EVENT_TIMEOUT ? parseInt(process.env.EVENT_TIMEOUT) : 15 * 60 * 1000
    },
    runtime: {
      podName: process.env.MY_POD_NAME,
      nodeEnv: process.env.NODE_ENV,
      pid: process.pid
    },
    salt: process.env.CHECKSUM_SALT,
    crossOrigin: process.env.CROSS_ORIGIN?.split(',') || []
  },
  aws: {
    s3: {
      bucketName: process.env.AWS_S3_BUCKET_NAME,
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_ACCESS_SECRET_KEY,
      region: process.env.AWS_REGION
    }
  },
  kafka: {
    topic: process.env.KAFKA_TOPIC!,
    options: {
      client: {
        clientId: 'dgw',
        brokers: process.env.KAFKA_HOST?.split(',') || []
      },
      consumer: {
        groupId: 'dgw-service'
      }
    }
  }
});
