import { BaseType } from './common.interface';
import { SchemaDefinition } from '@dsnp/parquetjs/dist/lib/declare';

export interface ParquetData extends Record<string, unknown> {
  ip: string;
  latitude: number;
  longitude: number;
  timestamp: number;
  app_type: string;
  version: string;
  platform: string;
  device: string;
  user_id: string;
  token: string;
  url: string;
  referral_url: string;
  title: string;
  engagement_time: number;
  event_name: string;
  event_parameters: { [key: string]: BaseType };
  user_parameters: { [key: string]: BaseType };
  user_agent: string;
  screen_resolution: string;
  viewport_size: string;
  campaign_source: string;
  campaign_medium: string;
  campaign_name: string;
  language: string;
}

export const ParquetSchema: SchemaDefinition = {
  ip: { type: 'UTF8' },
  latitude: { type: 'DOUBLE', optional: true },
  longitude: { type: 'DOUBLE', optional: true },
  timestamp: { type: 'INT64' },
  app_type: { type: 'UTF8' },
  version: { type: 'UTF8' },
  platform: { type: 'UTF8' },
  device: { type: 'UTF8' },
  user_id: { type: 'UTF8', optional: true },
  token: { type: 'UTF8' },
  url: { type: 'UTF8', optional: true },
  referral_url: { type: 'UTF8', optional: true },
  title: { type: 'UTF8' },
  engagement_time: { type: 'INT32', optional: true },
  event_name: { type: 'UTF8' },
  event_parameters: { type: 'JSON', optional: true },
  user_parameters: { type: 'JSON', optional: true },
  user_agent: { type: 'UTF8' },
  screen_resolution: { type: 'UTF8' },
  viewport_size: { type: 'UTF8' },
  campaign_source: { type: 'UTF8', optional: true },
  campaign_medium: { type: 'UTF8', optional: true },
  campaign_name: { type: 'UTF8', optional: true },
  language: { type: 'UTF8', optional: true }
};
