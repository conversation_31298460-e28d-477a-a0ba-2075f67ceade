import { BaseType } from './common.interface';

export interface BehavioralData {
  /** 事件校验码 */
  chk: string;
  /** 行为发生时的时间戳 */
  ts: number;
  /** 当前页面 url */
  dl: string;
  /** 重定向的 url */
  dr: string;
  /** 页面 PV 名称 */
  dt: string;
  /** 事件耗时 */
  et: number;
  /** 事件名 */
  en: string;
  /** 自定义事件参数 */
  ep: { [key: string]: BaseType };
  /** 自定义用户信息 */
  up: { [key: string]: BaseType };
  /** 用户屏幕分辨率 */
  sr: string;
  /** 可见窗口分辨率 */
  vp: string;
  /** Campaign Source */
  cs: string;
  /** Campaign Medium */
  cm: string;
  /** Campaign Name */
  cn: string;
  /** 设备语言 */
  lang: string;
}

export interface UserData {
  authorization: string;
  clientType: string;
  clientVersion: string;
  clientLatitude: number;
  clientLongitude: number;
  userId: string;
  userAgent: string;
  ipAddress: string;
  platform: string;
  device: string;
  behavior: BehavioralData[];
}
