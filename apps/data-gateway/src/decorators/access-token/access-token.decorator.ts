import { createParamDecorator, ExecutionContext } from '@nestjs/common';

/**
 * 用于获取客户端传递的 authorization 请求头中的 token
 */
export const AccessToken = createParamDecorator((data: string, ctx: ExecutionContext) => {
  const request = ctx.switchToHttp().getRequest();
  // 移除 Bearer 前缀
  const token = request.headers['authorization']?.split(' ')[1] || request.headers['authorization'];

  return token;
});
