import { IsString, <PERSON>N<PERSON>ber, IsObject, IsOptional } from 'class-validator';
import { BehavioralData } from '../interfaces/user.interface';
import { BaseType } from '../interfaces/common.interface';

export class BehaviorDto implements BehavioralData {
  @IsString()
  'chk': string;
  @IsNumber()
  'ts': number;
  @IsOptional()
  @IsString()
  'dl': string;
  @IsOptional()
  @IsString()
  'dr': string;
  @IsString()
  'dt': string;
  @IsOptional()
  @IsNumber()
  'et': number;
  @IsString()
  'en': string;
  @IsOptional()
  @IsObject()
  'ep': { [key: string]: BaseType };
  @IsOptional()
  @IsObject()
  'up': { [key: string]: BaseType };
  @IsString()
  'sr': string;
  @IsString()
  'vp': string;
  @IsOptional()
  @IsString()
  'cs': string;
  @IsOptional()
  @IsString()
  'cm': string;
  @IsOptional()
  @IsString()
  'cn': string;
  @IsOptional()
  @IsString()
  'lang': string;
}
