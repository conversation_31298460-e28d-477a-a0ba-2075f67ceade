import { IsString, IsN<PERSON>berString, IsOptional } from 'class-validator';
import { CommonParams } from '../interfaces/common.interface';

export class HeadersDto implements CommonParams {
  @IsString()
  'authorization': string;
  @IsString()
  'user-agent': string;
  @IsString()
  'hs-client-type': string;
  @IsString()
  'hs-client-version': string;
  @IsOptional()
  @IsNumberString()
  'hs-client-latitude': number;
  @IsOptional()
  @IsNumberString()
  'hs-client-longitude': number;
  @IsOptional()
  @IsString()
  'hs-user-id': string;
}
