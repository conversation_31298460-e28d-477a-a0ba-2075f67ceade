import { Logger, ValidationPipe, VersioningType } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { FastifyAdapter, NestFastifyApplication } from '@nestjs/platform-fastify';
import cluster from 'node:cluster';
import { availableParallelism } from 'node:os';
import process from 'node:process';
import { ApiServerModule } from './api-server/api-server.module';
import { KafkaConsumerModule } from './worker-services/kafka-consumer.module';
import configuration from './config/configuration';

const logger = new Logger('Bootstrap');
const numCPUs = availableParallelism();
const config = configuration();

let retries = 0;
const spawnWorkers = () => {
  setTimeout(
    () => {
      const workerCount = Object.keys(cluster.workers!).length;
      const maxWorkers = Math.min(numCPUs, config.common.worker.max);
      const workerDeficit = maxWorkers - workerCount;

      if (workerDeficit > 0) {
        for (let i = 0; i < workerDeficit; i++) {
          cluster.fork();
        }
      }
    },
    retries > 0 ? Math.pow(2, retries) * 1000 : 0
  );
  if (retries > 0) {
    logger.log(`Retry after ${Math.pow(2, retries)} seconds`);
  }
  retries++;
};

const apiBootstrap = async () => {
  const app = await NestFactory.create<NestFastifyApplication>(
    ApiServerModule,
    new FastifyAdapter({
      logger: true
    })
  );

  // CROS 跨域配置
  if (config.common.crossOrigin) {
    app.enableCors({
      origin: config.common.crossOrigin
    });
  }

  app.useGlobalPipes(new ValidationPipe());
  app.enableVersioning({
    type: VersioningType.URI
  });
  await app.listen(3000, '0.0.0.0');
};

const serviceBootstrap = async () => {
  const app = await NestFactory.createMicroservice<MicroserviceOptions>(KafkaConsumerModule, {
    transport: Transport.KAFKA,
    options: config.kafka.options
  });

  app.enableShutdownHooks();

  await app.listen();
};

if (cluster.isPrimary) {
  if (config.common.allowAPI) {
    //start api server (http)
    apiBootstrap()
      .then(async () => {
        logger.log(`Primary app running on ${process.pid}`);
      })
      .catch((e) => {
        logger.error(e);
        process.exit(1);
      });
  }
  cluster.on('exit', spawnWorkers);
  spawnWorkers();
} else if (cluster.isWorker) {
  logger.log(`Worker ${cluster.worker!.id}:${process.pid} started`);
  //start kafka consumer (microservice)
  serviceBootstrap()
    .then(async () => {
      logger.log(`Kafka worker is running on ${process.pid}`);
    })
    .catch((e) => {
      logger.error(e);
      process.exit(1);
    });
}
