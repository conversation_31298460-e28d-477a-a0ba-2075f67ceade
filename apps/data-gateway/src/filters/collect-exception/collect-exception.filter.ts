import { ExceptionFilter, Catch, ArgumentsHost, BadRequestException, HttpStatus, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { FastifyReply } from 'fastify';

@Catch(BadRequestException)
@Injectable()
export class CollectionExceptionFilter implements ExceptionFilter {
  constructor(private readonly configService: ConfigService) {}

  catch(exception: BadRequestException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<FastifyReply>();
    const status = HttpStatus.NO_CONTENT;

    // 测试环境开启 debug 模式
    // 客户端请求 debug 模式
    if (this.configService.get('common.debug')) {
      throw exception;
    }

    // collect API 仅做数据校验，使用返回 204 状态码
    response.status(status).send();
  }
}
