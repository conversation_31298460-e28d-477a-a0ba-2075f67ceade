import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { ArgumentsHost, BadRequestException, HttpStatus } from '@nestjs/common';
import { CollectionExceptionFilter } from './collect-exception.filter';

describe('CollectionExceptionFilter', () => {
  let filter: CollectionExceptionFilter;
  let configService: ConfigService;
  let mockArgumentsHost: ArgumentsHost;
  let mockResponse: any;

  beforeEach(async () => {
    // Mock the Fastify response object
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      send: jest.fn()
    };

    // Mock ArgumentsHost
    mockArgumentsHost = {
      switchToHttp: jest.fn(() => ({
        getResponse: jest.fn(() => mockResponse)
      }))
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CollectionExceptionFilter,
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn()
          }
        }
      ]
    }).compile();

    filter = module.get<CollectionExceptionFilter>(CollectionExceptionFilter);
    configService = module.get<ConfigService>(ConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(filter).toBeDefined();
  });

  describe('catch', () => {
    let badRequestException: BadRequestException;

    beforeEach(() => {
      badRequestException = new BadRequestException('Test error');
    });

    it('should throw the exception when debug mode is enabled', () => {
      // Arrange
      jest.spyOn(configService, 'get').mockReturnValue(true);

      // Act & Assert
      expect(() => {
        filter.catch(badRequestException, mockArgumentsHost);
      }).toThrow(badRequestException);

      expect(configService.get).toHaveBeenCalledWith('common.debug');
    });

    it('should return 204 status when debug mode is disabled', () => {
      // Arrange
      jest.spyOn(configService, 'get').mockReturnValue(false);

      // Act
      filter.catch(badRequestException, mockArgumentsHost);

      // Assert
      expect(configService.get).toHaveBeenCalledWith('common.debug');
      expect(mockArgumentsHost.switchToHttp).toHaveBeenCalled();
      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.NO_CONTENT);
      expect(mockResponse.send).toHaveBeenCalledWith();
    });

    it('should return 204 status when debug mode is undefined', () => {
      // Arrange
      jest.spyOn(configService, 'get').mockReturnValue(undefined);

      // Act
      filter.catch(badRequestException, mockArgumentsHost);

      // Assert
      expect(configService.get).toHaveBeenCalledWith('common.debug');
      expect(mockArgumentsHost.switchToHttp).toHaveBeenCalled();
      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.NO_CONTENT);
      expect(mockResponse.send).toHaveBeenCalledWith();
    });

    it('should handle the exception and send proper response structure', () => {
      // Arrange
      jest.spyOn(configService, 'get').mockReturnValue(false);

      // Act
      filter.catch(badRequestException, mockArgumentsHost);

      // Assert
      expect(mockResponse.status).toHaveBeenCalledTimes(1);
      expect(mockResponse.send).toHaveBeenCalledTimes(1);
      expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.NO_CONTENT);
      expect(mockResponse.send).toHaveBeenCalledWith();
    });
  });
});
