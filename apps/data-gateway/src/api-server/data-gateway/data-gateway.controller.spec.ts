import { Test, TestingModule } from '@nestjs/testing';
import { DataGatewayController } from './data-gateway.controller';
import { DataGatewayService } from './data-gateway.service';

describe('DataGatewayController', () => {
  let dataGatewayController: DataGatewayController;

  beforeEach(async () => {
    const app: TestingModule = await Test.createTestingModule({
      controllers: [DataGatewayController],
      providers: [DataGatewayService]
    }).compile();

    dataGatewayController = app.get<DataGatewayController>(DataGatewayController);
  });

  describe('root', () => {
    it('should return "Data gateway service"', () => {
      expect(dataGatewayController.getHello()).toBe('Data gateway service');
    });
  });
});
