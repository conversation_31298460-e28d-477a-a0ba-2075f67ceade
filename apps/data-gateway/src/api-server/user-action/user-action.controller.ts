import {
  Body,
  Controller,
  HttpCode,
  HttpStatus,
  Logger,
  Post,
  UseFilters,
  ValidationPipe,
  Version
} from '@nestjs/common';
import { RealIP } from 'nestjs-real-ip';
import { UAParser } from 'ua-parser-js';
import { UserActionService } from './user-action.service';
import { UserData } from '../../interfaces/user.interface';
import { Headers } from '../../decorators/headers/headers.decorator';
import { HeadersDto } from '../../dtos/header';
import { CollectionExceptionFilter } from '../../filters/collect-exception/collect-exception.filter';
import { CollectDataDto } from '../../dtos/collect';

@Controller('collect')
export class UserActionController {
  private readonly logger = new Logger(UserActionController.name);

  constructor(private readonly userActionService: UserActionService) {}

  @Version('1')
  @Post()
  @HttpCode(HttpStatus.NO_CONTENT)
  @UseFilters(CollectionExceptionFilter)
  collectUserAction(
    @Headers(new ValidationPipe({ validateCustomDecorators: true })) headers: HeadersDto,
    @Body(new ValidationPipe()) body: CollectDataDto,
    @RealIP() ip: string
  ) {
    this.logger.log(`Start collecting. user id ${headers['hs-user-id']}, user token ${headers['authorization']}`);

    const ua = headers['user-agent'] || '';
    // 组织请求中的用户数据
    const clientInfo: UserData = {
      authorization: headers['authorization'],
      clientType: headers['hs-client-type'],
      clientVersion: headers['hs-client-version'],
      clientLatitude: headers['hs-client-latitude'],
      clientLongitude: headers['hs-client-longitude'],
      userId: headers['hs-user-id'],
      userAgent: ua,
      ipAddress: ip,
      platform: this.getPlatform(ua),
      device: this.getDevice(ua),
      behavior: body.data
    };

    // 将用户行为事件推送至 kafka 消息队列
    this.userActionService.processUserAction(clientInfo);
  }

  /**
   * 获取用户设备平台类型
   * @returns
   */
  private getPlatform(ua: string) {
    const uaParser = new UAParser(ua);
    const os = uaParser.getOS();

    if (os.name?.match(/Android/i) || ua.match(/Android/i)) {
      return 'android';
    }

    if (os.name?.match(/iOS/i) || ua.match(/iOS/i)) {
      return 'ios';
    }

    if (os.name?.match(/Windows/i) || ua.match(/Windows/i)) {
      return 'windows';
    }

    if (os.name?.match(/Mac/i) || ua.match(/Mac/i)) {
      return 'mac';
    }

    if (os.name?.match(/Linux/i) || ua.match(/Linux/i)) {
      return 'linux';
    }

    return 'unknown';
  }

  /**
   * 获取用户设备类型
   * @returns
   */
  private getDevice(ua: string) {
    const uaParser = new UAParser(ua);
    const device = uaParser.getDevice();

    return device.type || 'unknown';
  }
}
