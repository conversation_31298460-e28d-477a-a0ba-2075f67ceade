import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { UserActionController } from './user-action.controller';
import { UserActionService } from './user-action.service';

@Module({
  imports: [
    ClientsModule.registerAsync([
      {
        name: 'KAFKA_SERVICE',
        useFactory: async (configService: ConfigService) => ({
          transport: Transport.KAFKA,
          options: configService.get('kafka.options')
        }),
        inject: [ConfigService]
      }
    ])
  ],
  controllers: [UserActionController],
  providers: [UserActionService]
})
export class UserActionModule {}
