import { Inject, Injectable, Logger, OnApplicationBootstrap } from '@nestjs/common';
import { ClientKafka } from '@nestjs/microservices';
import { UserData } from '../../interfaces/user.interface';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class UserActionService implements OnApplicationBootstrap {
  private readonly logger = new Logger(UserActionService.name);

  constructor(
    private readonly configService: ConfigService,
    @Inject('KAFKA_SERVICE') private kafkaClient: ClientKafka
  ) {}

  async onApplicationBootstrap(): Promise<void> {
    await this.kafkaClient.connect();
  }

  /**
   * 推送用户行为事件至 kafka 队列中
   * @param userData 行为数据
   */
  processUserAction(userData: UserData) {
    this.logger.log(
      `kafka event emitted successfully. user id ${userData.userId}, user token ${userData.authorization}`
    );
    this.kafkaClient.emit(this.configService.get('kafka.topic'), userData);
  }
}
