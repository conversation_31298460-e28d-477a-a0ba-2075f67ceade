import { Module } from '@nestjs/common';
import { DataGatewayModule } from './data-gateway/data-gateway.module';
import { UserActionModule } from './user-action/user-action.module';
import { ConfigModule } from '@nestjs/config';
import configuration from '../config/configuration';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      cache: true,
      load: [configuration]
    }),
    DataGatewayModule,
    UserActionModule
  ]
})
export class ApiServerModule {}
