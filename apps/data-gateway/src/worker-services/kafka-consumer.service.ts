import crypto from 'crypto';
import { Injectable, Logger, BeforeApplicationShutdown } from '@nestjs/common';
import { UserData } from '../interfaces/user.interface';
import { ConfigService } from '@nestjs/config';
import { ParquetService } from '../shared/parquet/parquet.service';
import { AWSS3Service } from '../shared/aws-s3/aws-s3.service';
import { ParquetData } from '../interfaces/parquet.interface';

@Injectable()
export class KafkaConsumerService implements BeforeApplicationShutdown {
  private readonly logger = new Logger(KafkaConsumerService.name);
  private eventCount = 0;
  private timestamp = 0;
  private parquetFileName: string = '';

  constructor(
    private readonly parquetService: ParquetService,
    private readonly awsS3Service: AWSS3Service,
    private readonly configService: ConfigService
  ) {
    this.logger.log(`Start initializing. parquetFileName ${this.parquetFileName}`);

    this.initParquetFileName();
    this.checkParquetFileExists();

    this.logger.log(`End initialization. parquetFileName ${this.parquetFileName}`);
  }

  beforeApplicationShutdown() {
    const hasParquet = this.parquetService.getRowCount() > 0;

    this.logger.log(
      `BeforeApplicationShutdown, Start generating & uploading Parquet. hasParquet ${hasParquet}, parquetFileName: ${this.parquetFileName}`
    );

    // 在程序关闭前，检查是否存在 parquet 文件未上传，是则触发上传
    this.checkParquetFileExists();

    // 若缓存中存在数据，则生成/上传 parquet 文件
    if (hasParquet) {
      this.generateAndUploadParquet(this.parquetFileName);
    }
  }

  /**
   * 初始化 parquet 文件名
   */
  initParquetFileName() {
    this.parquetFileName = this.parquetService.generateFileName();
  }

  /**
   * 消费 kafka 消息
   * @param userData 用户行为事件数据
   */
  async processMessage(userData: UserData) {
    this.logger.log(
      `kafka event received successfully. user id ${userData.userId}, user token ${userData.authorization}`
    );

    // 将用户行为事件转化成 parquet 数据格式
    const parquetList = this.parquetService.transformEventToParquetRow(userData);
    const newRows: ParquetData[] = [];

    try {
      // 保存当前时间戳，用于判断事件是否超过 15 分钟，触发 parquet 文件生成及上传
      if (!this.timestamp) {
        this.timestamp = Date.now();
      }

      // 将 parquet 数据合并到当前 parquet 内容中
      for (const [index, parquetItem] of parquetList.entries()) {
        const chk = userData.behavior[index].chk;

        // 校验事件是否合法
        if (this.checkSum(chk, parquetItem)) {
          newRows.push(parquetItem);
          // 递增事件数计数
          this.eventCount++;
        } else {
          this.logger.warn(
            `processMessage checkSum failed. user id ${userData.userId}, user token ${userData.authorization}`
          );
        }
      }

      // 将 parquet 数据重新写入内存中
      this.parquetService.writeRows(newRows);

      // 检查是否可生成 parquet 并上传到 AWS S3
      const thresholdChecked = this.checkThreshold();
      const hasParquet = this.parquetService.getRowCount() > 0;

      this.logger.log(
        `Checking Rules. user id ${userData.userId}, user token ${userData.authorization}, thresholdChecked ${thresholdChecked}, hasParquet ${hasParquet}`
      );

      if (thresholdChecked && hasParquet) {
        this.logger.log(
          `Start generating & uploading Parquet. user id ${userData.userId}, user token ${userData.authorization}, parquetFileName ${this.parquetFileName}`
        );

        this.generateAndUploadParquet(this.parquetFileName);
      } else {
        this.logger.log(
          `Failed to generate and upload parquet. thresholdChecked ${thresholdChecked}, hasParquet ${hasParquet}, eventCount ${this.eventCount}, timestamp ${this.timestamp}`
        );
      }
    } catch (error) {
      this.logger.error(
        'processMessage catch write error',
        error,
        this.parquetService.getFilePath(this.parquetFileName)
      );
    }
  }

  /**
   * 检查是否符合生成/上传 parquet 条件
   * 超过 200 个事件或者 15 分钟，则触发上传
   * @returns
   */
  private checkThreshold() {
    const { eventCount, timestamp } = this;
    const EVENT_COUNT_THRESHOLD = this.configService.get('common.threshold.eventCount');
    const TIMEOUT_THRESHOLD = this.configService.get('common.threshold.timeout');

    return eventCount >= EVENT_COUNT_THRESHOLD || Date.now() - timestamp >= TIMEOUT_THRESHOLD;
  }

  /**
   * 生成并上传 parquet 文件
   * @param fileName parquet 文件名
   */
  private async generateAndUploadParquet(fileName: string) {
    const rows = [...this.parquetService.readRows()];

    // 生成/上传 parquet 文件的同时，重置配置。
    this.reset();

    this.logger.log(`Start uploading Parquet file. fileName ${fileName}, rows ${rows.length}`);

    // 生成 parquet 文件
    await this.parquetService.writeParquet(fileName, rows);
    await this.uploadToS3(fileName);

    this.logger.log(`Parquet File Upload Ended. fileName ${fileName}`);
  }

  /**
   * 上传到 AWS S3
   * @param parquetName parquet 文件名
   */
  private async uploadToS3(fileName: string) {
    const filePath = this.parquetService.getFilePath(fileName);

    this.logger.log(`Start upload to AWS S3. fileName ${fileName} filePath: ${filePath}`);

    // 上传到 AWS S3
    await this.awsS3Service.upload(filePath);

    // 上传到 AWS S3 后，删除本地 parquet 文件
    this.parquetService.deleteParquet(fileName);

    this.logger.log(`End upload to AWS S3. fileName ${fileName}`);
  }

  /**
   * 检查本地是否存在 parquet 文件
   * 存在，则触发上传至 AWS S3
   */
  private async checkParquetFileExists() {
    const fileNames = await this.parquetService.getAllFileName();

    if (fileNames.length > 0) {
      this.logger.log(`Local Parquet files, ${fileNames.join(';')}`);
    }

    for (const fileName of fileNames) {
      this.uploadToS3(fileName);
    }
  }

  /**
   * 重置当前配置
   * 用于生成新的 parquet 文件。
   */
  private reset() {
    this.logger.log(
      `Start Reset. eventCount ${this.eventCount} timestamp ${this.timestamp} parquetFileName ${this.parquetFileName} parquetRowCount ${this.parquetService.getRowCount()}`
    );

    // 事件数计数清零
    this.eventCount = 0;
    // 时间重置
    this.timestamp = 0;
    // 生成 parquet 新文件
    this.initParquetFileName();
    // 清空缓存中的 parquet 数据
    this.parquetService.cleanRows();

    this.logger.log(
      `Reset finished. eventCount ${this.eventCount} timestamp ${this.timestamp} parquetFileName ${this.parquetFileName} parquetRowCount ${this.parquetService.getRowCount()}`
    );
  }

  /**
   * 校验 parquet数据 是否合法
   * @param chk 事件校验码
   * @param parquetData parquet 数据
   * @returns
   */
  private checkSum(chk: string, parquetData: ParquetData) {
    // 拼接字符串
    const str = `${parquetData.token}${parquetData.timestamp}${parquetData.event_name}${this.configService.get('common.salt')}`;
    // 创建 MD5 哈希对象
    const hash = crypto.createHash('md5');
    // 更新哈希内容
    hash.update(str);
    // 生成 hex 验证码
    const hexCode = hash.digest('hex');

    if (hexCode !== chk) {
      this.logger.log(
        `checkSum failed. chkClient ${chk}, chkServer ${hexCode}`,
        `user token ${parquetData.token} timestamp ${parquetData.timestamp} event_name ${parquetData.event_name}`
      );
    }

    return hexCode === chk;
  }
}
