import { <PERSON>, Logger } from '@nestjs/common';
import { Ctx, EventPattern, KafkaContext, Payload } from '@nestjs/microservices';
import { KafkaConsumerService } from './kafka-consumer.service';
import { UserData } from '../interfaces/user.interface';
import configuration from '../config/configuration';

const config = configuration();

@Controller()
export class KafkaConsumerController {
  private readonly logger = new Logger(KafkaConsumerController.name);

  constructor(private readonly kafkaConsumerService: KafkaConsumerService) {}

  // Kafka topic naming convention: <application>.<data type>.<event type>
  @EventPattern(config.kafka.topic)
  consume(@Payload() data: UserData, @Ctx() context: KafkaContext) {
    this.logger.log(
      `Start consuming. user id ${data.userId}, user token ${data.authorization}, topic ${context.getTopic()}`
    );
    this.kafkaConsumerService.processMessage(data);
  }
}
