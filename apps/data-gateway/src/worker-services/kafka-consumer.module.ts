import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { KafkaConsumerController } from './kafka-consumer.controller';
import { KafkaConsumerService } from './kafka-consumer.service';
import { ParquetModule } from '../shared/parquet/parquet.module';
import { FileModule } from '../shared/file/file.module';
import { AwsS3Module } from '../shared/aws-s3/aws-s3.module';
import configuration from '../config/configuration';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      cache: true,
      load: [configuration]
    }),
    ParquetModule,
    FileModule,
    AwsS3Module
  ],
  controllers: [KafkaConsumerController],
  providers: [KafkaConsumerService]
})
export class KafkaConsumerModule {}
