import fs, { promises as fsp } from 'fs';
import path from 'path';
import os from 'os';
import { Injectable, Logger } from '@nestjs/common';

const TEMP_FILE_PATH = path.resolve(os.tmpdir(), 'hs_temp_files.txt');

@Injectable()
export class FileService {
  private readonly logger = new Logger(FileService.name);

  /**
   * 写入临时文件
   * @param newRow 每行数据
   */
  async writeToTempFile<T>(newRow: T) {
    try {
      if (newRow != null) {
        await fsp.appendFile(TEMP_FILE_PATH, JSON.stringify(newRow) + '\n', 'utf-8');
      }
    } catch (error) {
      this.logger.error('writeToTempFile error', error);
    }
  }

  /**
   * 读取临时文件中的数据
   * @returns
   */
  async readFromTempFile<T>(): Promise<T[]> {
    try {
      const data = await fsp.readFile(TEMP_FILE_PATH, 'utf-8');
      // 遍历读取每一行的数据
      const lines = data.split('\n').filter((line) => line.trim() !== '');
      // 拼接成数据返回
      return lines.map((line) => JSON.parse(line));
    } catch (error) {
      this.logger.error('readFromTempFile error', error);
      return []; // 文件不存在，返回空数组
    }
  }

  /**
   * 清空临时文件内容
   */
  async cleanTempFile() {
    try {
      await fsp.writeFile(TEMP_FILE_PATH, '', 'utf-8');
    } catch (error) {
      this.logger.error('cleanTempFile error', error);
    }
  }

  /**
   * 删除临时文件
   */
  async deleteTempFile() {
    try {
      await fsp.unlink(TEMP_FILE_PATH);
    } catch (error) {
      this.logger.error('deleteTempFile error', error);
    }
  }

  /**
   * 判断临时文件是否存在
   * @returns
   */
  async isTempfileExists(): Promise<boolean> {
    try {
      await fsp.access(TEMP_FILE_PATH);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 为本地文件创建可读流
   * @param filePath 文件路径
   * @returns
   */
  createReadStream(filePath: string) {
    return fs.createReadStream(filePath);
  }
}
