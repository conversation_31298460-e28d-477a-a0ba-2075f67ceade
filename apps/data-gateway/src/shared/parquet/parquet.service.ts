import path from 'path';
import os from 'os';
import { promises as fsp } from 'fs';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import parquet, { ParquetWriter } from '@dsnp/parquetjs';
import { UserData } from '../../interfaces/user.interface';
import { ParquetData, ParquetSchema } from '../../interfaces/parquet.interface';

const TEMP_DIR = path.resolve(os.tmpdir(), 'parquet');

@Injectable()
export class ParquetService {
  private readonly logger = new Logger(ParquetService.name);
  private rows: ParquetData[] = [];

  constructor(private readonly configService: ConfigService) {
    // 创建临时文件目录
    this.createTempDir();
  }

  /**
   * 生成 parquet 文件名
   * 格式为 [timestamp]_[podName]_[pid].parquet
   * @returns
   */
  generateFileName() {
    const timestamp = Date.now();
    const podName = this.configService.get('common.runtime.podName');
    const pid = this.configService.get('common.runtime.pid');

    return `${timestamp}_${podName}_${pid}.parquet`;
  }

  /**
   * 将 parquet 数据写入缓存中
   * @param data parquet 数据
   */
  writeRows(data: ParquetData[]) {
    this.rows = [...this.rows, ...data];
  }

  /**
   * 读取当前缓存中的 parquet 数据
   * @returns
   */
  readRows() {
    return this.rows;
  }

  /**
   * 清空当前 parquet 缓存数据
   */
  cleanRows() {
    this.rows = [];
  }

  /**
   * 获取当前缓存中的 parquet 数据大小
   * @returns
   */
  getRowCount() {
    return this.rows.length;
  }

  /**
   * 往 parquet 文件中写入数据
   * @param fileName Parquet 文件名
   * @param data 数据
   */
  async writeParquet(fileName: string, data: ParquetData[]) {
    // 创建 parquet schema
    const schema = this.createParquetSchema();
    let writer: ParquetWriter | null = null;

    // 根据文件名从临时目录中创建 parquet 文件
    try {
      writer = await parquet.ParquetWriter.openFile(schema, this.getFilePath(fileName));

      if (writer) {
        for (const item of data) {
          await writer.appendRow(item);
        }
      }
    } catch (error) {
      this.logger.error('writeParquet error', error);
    } finally {
      // 保证 writer stream 正常关闭
      if (writer) {
        await writer.close();
      }
    }
  }

  /**
   * 从 parquet 文件中读取数据
   * @param fileName Parquet 文件名
   * @returns
   */
  async readParquet(fileName: string): Promise<ParquetData[]> {
    try {
      // 根据文件名从临时目录中读取 parquet 文件
      const reader = await parquet.ParquetReader.openFile(this.getFilePath(fileName));

      if (reader) {
        const cursor = reader.getCursor();
        const rows: ParquetData[] = [];

        let record: ParquetData | null = null;

        while ((record = (await cursor.next()) as unknown as ParquetData)) {
          rows.push(record);
        }

        await reader.close();

        return rows;
      }
    } catch (error) {
      this.logger.error('readParquet error', error, this.getFilePath(fileName));
    }

    return [];
  }

  /**
   * 删除 parquet 文件
   * @param fileName 指定 parquet 文件名
   */
  async deleteParquet(fileName: string) {
    try {
      await fsp.unlink(this.getFilePath(fileName));
    } catch (error) {
      this.logger.error('deleteParquet error', error);
    }
  }

  /**
   * 创建 Parquet Schema
   */
  private createParquetSchema() {
    return new parquet.ParquetSchema(ParquetSchema);
  }

  /**
   * 将用户行为事件转换为 Parquet 格式
   */
  transformEventToParquetRow(data: UserData) {
    return (
      data.behavior.map?.(
        (behavior): ParquetData => ({
          ip: data.ipAddress,
          latitude: data.clientLatitude,
          longitude: data.clientLongitude,
          timestamp: behavior.ts,
          app_type: data.clientType,
          version: data.clientVersion,
          platform: data.platform,
          device: data.device,
          user_id: data.userId,
          token: data.authorization,
          url: behavior.dl,
          referral_url: behavior.dr,
          title: behavior.dt,
          engagement_time: behavior.et,
          event_name: behavior.en,
          event_parameters: behavior.ep,
          user_parameters: behavior.up,
          user_agent: data.userAgent,
          screen_resolution: behavior.sr,
          viewport_size: behavior.vp,
          campaign_source: behavior.cs,
          campaign_medium: behavior.cm,
          campaign_name: behavior.cn,
          language: behavior.lang
        })
      ) || []
    );
  }

  /**
   * 获取临时目录下 /parquet 目录中的所有 Parquet 文件名
   * @returns
   */
  async getAllFileName(): Promise<string[]> {
    try {
      const files = await fsp.readdir(TEMP_DIR);

      return files.filter((fileName) => fileName.endsWith('.parquet'));
    } catch {
      return [];
    }
  }

  /**
   * 创建 parquet 文件临时目录
   */
  private async createTempDir() {
    try {
      await fsp.access(TEMP_DIR);
    } catch {
      await fsp.mkdir(TEMP_DIR, { recursive: true });
    }
  }

  /**
   * 根据 parquet 文件名，获取 parquet 文件完整路径
   * @param fileName parquet 文件名
   * @returns
   */
  getFilePath(fileName: string) {
    return path.resolve(TEMP_DIR, fileName);
  }
}
