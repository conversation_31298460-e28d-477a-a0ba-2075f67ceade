import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { ParquetService } from './parquet.service';

describe('ParquetService', () => {
  let service: ParquetService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ParquetService, ConfigService]
    }).compile();

    service = module.get<ParquetService>(ParquetService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
