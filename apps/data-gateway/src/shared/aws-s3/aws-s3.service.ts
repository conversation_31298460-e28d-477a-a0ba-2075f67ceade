import path from 'path';
import fs, { promises as fsp } from 'fs';
import { Injectable, Logger } from '@nestjs/common';
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AWSS3Service {
  private readonly logger = new Logger(AWSS3Service.name);
  private s3Client: S3Client;

  constructor(private readonly configService: ConfigService) {
    const { region, accessKeyId, secretAccessKey } = this.configService.get('aws.s3');

    // 初始化 AWSS3 SDK
    this.s3Client = new S3Client({
      region,
      credentials: {
        accessKeyId,
        secretAccessKey
      }
    });
  }

  /**
   * 上传指定文件到 AWS S3
   * 存储路径为 YYYY/MM/DD/fileName
   */
  async upload(filePath: string) {
    if (await this.isFileExists(filePath)) {
      const date = new Date();
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const fileName = path.basename(filePath);
      const s3Path = `${year}/${month}/${day}/${fileName}`;
      const bucketName = this.configService.get('aws.s3.bucketName');

      this.logger.log('upload', date, s3Path, bucketName);

      try {
        const command = new PutObjectCommand({
          Bucket: bucketName,
          Key: s3Path,
          Body: fs.createReadStream(filePath)
        });
        await this.s3Client.send(command);
      } catch (error) {
        this.logger.error('upload failed', error);
      }
    } else {
      this.logger.warn('upload file not exists', filePath);
    }
  }

  /**
   * 判断本地文件是否存在
   * @param filePath 文件路径
   * @returns
   */
  private async isFileExists(filePath: string): Promise<boolean> {
    try {
      await fsp.access(filePath);
      return true;
    } catch {
      return false;
    }
  }
}
