import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { AWSS3Service } from './aws-s3.service';

describe('AWSS3Service', () => {
  let service: AWSS3Service;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [AWSS3Service]
    })
      .useMocker((token) => {
        if (token === ConfigService) {
          return {
            get: jest.fn().mockReturnValue({
              region: 'ca-central-1',
              accessKeyId: 'test-key-id',
              secretAccessKey: 'test-access-key'
            })
          };
        }
      })
      .compile();

    service = module.get<AWSS3Service>(AWSS3Service);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
