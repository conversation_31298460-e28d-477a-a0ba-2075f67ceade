export default () => ({
  common: {
    runtime: {
      podName: process.env.MY_POD_NAME,
      nodeEnv: process.env.NODE_ENV,
      pid: process.pid
    }
  },
  aws: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_ACCESS_SECRET_KEY,
    region: process.env.AWS_REGION
  },
  llm: {
    Claude: {
      apiKey: process.env.ANTHROPIC_API_KEY
    }
  },
  kafka: {
    topic: 'agent-queue-topic',
    topicLiveChat: 'live-chat-topic',
    options: {
      client: {
        clientId: 'aq',
        brokers: process.env.KAFKA_HOSTS?.split(',') || []
      },
      consumer: {
        groupId: 'aq-service'
      }
    }
  },
  liveChat: {
    accessToken: process.env.LIVECHAT_ACCESS_TOKEN,
    agentUserId: process.env.LIVECHAT_AGENT_ID
  },
  agents: {
    difyWorkflowUrl: process.env.DIFY_WORKFLOW_URL || 'http://localhost/v1/workflows/run',
    difyUser: process.env.DIFY_USER || 'test_user',
    //parse-freshdesk-review
    agent1: {
      accessToken: process.env.DIFY_TOKEN_FRESHDESK_REVIEW
    },
    //identify intention
    agent2: {
      accessToken: process.env.DIFY_TOKEN_IDENTIFY_INTENTION
    }
  }
});
