import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from '@nestjs/common';
import { KafkaWorkerService } from './kafka-worker.service';
import { ParseLotDimensionService } from '../parse-lot-dimension/parse-lot-dimension';
import { ParseFreshdeskReviewsService } from '../parse-freshdesk-reviews/parse-freshdesk-reviews';
import { JobMessage } from '../dtos/common-types';

describe('KafkaWorkerService', () => {
  let service: KafkaWorkerService;
  let parseLotDimensionService: jest.Mocked<ParseLotDimensionService>;
  let parseFreshdeskReviewsService: jest.Mocked<ParseFreshdeskReviewsService>;
  let loggerLogSpy: jest.SpyInstance;
  let loggerWarnSpy: jest.SpyInstance;

  beforeEach(async () => {
    const mockParseLotDimensionService = {
      parseLotDimension: jest.fn()
    };

    const mockParseFreshdeskReviewsService = {
      processReviewFile: jest.fn()
    };

    // Mock Logger methods before service instantiation
    loggerLogSpy = jest.spyOn(Logger.prototype, 'log').mockImplementation();
    loggerWarnSpy = jest.spyOn(Logger.prototype, 'warn').mockImplementation();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        KafkaWorkerService,
        {
          provide: ParseLotDimensionService,
          useValue: mockParseLotDimensionService
        },
        {
          provide: ParseFreshdeskReviewsService,
          useValue: mockParseFreshdeskReviewsService
        }
      ]
    }).compile();

    service = module.get<KafkaWorkerService>(KafkaWorkerService);
    parseLotDimensionService = module.get(ParseLotDimensionService);
    parseFreshdeskReviewsService = module.get(ParseFreshdeskReviewsService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    it('should be defined', () => {
      // Assert
      expect(service).toBeDefined();
    });

    it('should initialize logger with correct name', () => {
      // Assert
      expect(service['logger']).toBeInstanceOf(Logger);
    });

    it('should log initialization message on construction', () => {
      // Assert
      expect(loggerLogSpy).toHaveBeenCalledWith('Start initializing Kafka Worker Service');
    });

    it('should have access to injected services', () => {
      // Assert
      expect(service['parseLotDimensionService']).toBe(parseLotDimensionService);
      expect(service['parseFreshdeskReviews']).toBe(parseFreshdeskReviewsService);
    });
  });

  describe('processMessage', () => {
    describe('parseLotDimension job type', () => {
      it('should successfully process parseLotDimension job message', async () => {
        // Arrange
        const jobMessage: JobMessage = {
          jobKey: 'parseLotDimension',
          payload: { lotId: '12345', dimensions: 'width:100,height:200' }
        };

        parseLotDimensionService.parseLotDimension.mockResolvedValue(undefined);

        // Act
        await service.processMessage(jobMessage);

        // Assert
        expect(parseLotDimensionService.parseLotDimension).toHaveBeenCalledTimes(1);
        expect(parseLotDimensionService.parseLotDimension).toHaveBeenCalledWith(jobMessage.payload);
        expect(parseFreshdeskReviewsService.processReviewFile).not.toHaveBeenCalled();
        expect(loggerWarnSpy).not.toHaveBeenCalled();
      });

      it('should handle empty payload for parseLotDimension job', async () => {
        // Arrange
        const jobMessage: JobMessage = {
          jobKey: 'parseLotDimension',
          payload: {}
        };

        parseLotDimensionService.parseLotDimension.mockResolvedValue(undefined);

        // Act
        await service.processMessage(jobMessage);

        // Assert
        expect(parseLotDimensionService.parseLotDimension).toHaveBeenCalledTimes(1);
        expect(parseLotDimensionService.parseLotDimension).toHaveBeenCalledWith(jobMessage.payload);
        expect(parseFreshdeskReviewsService.processReviewFile).not.toHaveBeenCalled();
        expect(loggerWarnSpy).not.toHaveBeenCalled();
      });

      it('should handle error when ParseLotDimensionService throws exception', async () => {
        // Arrange
        const jobMessage: JobMessage = {
          jobKey: 'parseLotDimension',
          payload: { lotId: '12345', dimensions: 'invalid-format' }
        };

        const error = new Error('Failed to parse lot dimensions');
        parseLotDimensionService.parseLotDimension.mockRejectedValue(error);

        // Act & Assert
        await expect(service.processMessage(jobMessage)).rejects.toThrow('Failed to parse lot dimensions');
        expect(parseLotDimensionService.parseLotDimension).toHaveBeenCalledTimes(1);
        expect(parseLotDimensionService.parseLotDimension).toHaveBeenCalledWith(jobMessage.payload);
        expect(parseFreshdeskReviewsService.processReviewFile).not.toHaveBeenCalled();
      });

      it('should handle string error when ParseLotDimensionService rejects with string', async () => {
        // Arrange
        const jobMessage: JobMessage = {
          jobKey: 'parseLotDimension',
          payload: { lotId: '12345' }
        };

        const errorMessage = 'Service unavailable';
        parseLotDimensionService.parseLotDimension.mockRejectedValue(errorMessage);

        // Act & Assert
        await expect(service.processMessage(jobMessage)).rejects.toBe(errorMessage);
        expect(parseLotDimensionService.parseLotDimension).toHaveBeenCalledTimes(1);
        expect(parseLotDimensionService.parseLotDimension).toHaveBeenCalledWith(jobMessage.payload);
      });
    });

    describe('parseFreshDeskReview job type', () => {
      it('should successfully process parseFreshDeskReview job message', async () => {
        // Arrange
        const jobMessage: JobMessage = {
          jobKey: 'parseFreshDeskReview',
          payload: { path: '/path/to/review/file.json' }
        };

        parseFreshdeskReviewsService.processReviewFile.mockResolvedValue('done');

        // Act
        await service.processMessage(jobMessage);

        // Assert
        expect(parseFreshdeskReviewsService.processReviewFile).toHaveBeenCalledTimes(1);
        expect(parseFreshdeskReviewsService.processReviewFile).toHaveBeenCalledWith('/path/to/review/file.json');
        expect(parseLotDimensionService.parseLotDimension).not.toHaveBeenCalled();
        expect(loggerWarnSpy).not.toHaveBeenCalled();
      });

      it('should handle payload with numeric path value for parseFreshDeskReview job', async () => {
        // Arrange
        const jobMessage: JobMessage = {
          jobKey: 'parseFreshDeskReview',
          payload: { path: '123456' }
        };

        parseFreshdeskReviewsService.processReviewFile.mockResolvedValue('done');

        // Act
        await service.processMessage(jobMessage);

        // Assert
        expect(parseFreshdeskReviewsService.processReviewFile).toHaveBeenCalledTimes(1);
        expect(parseFreshdeskReviewsService.processReviewFile).toHaveBeenCalledWith('123456');
        expect(parseLotDimensionService.parseLotDimension).not.toHaveBeenCalled();
        expect(loggerWarnSpy).not.toHaveBeenCalled();
      });

      it('should handle payload with additional properties for parseFreshDeskReview job', async () => {
        // Arrange
        const jobMessage: JobMessage = {
          jobKey: 'parseFreshDeskReview',
          payload: { path: '/reviews/data.csv', reviewId: '789', source: 'freshdesk' }
        };

        parseFreshdeskReviewsService.processReviewFile.mockResolvedValue('done');

        // Act
        await service.processMessage(jobMessage);

        // Assert
        expect(parseFreshdeskReviewsService.processReviewFile).toHaveBeenCalledTimes(1);
        expect(parseFreshdeskReviewsService.processReviewFile).toHaveBeenCalledWith('/reviews/data.csv');
        expect(parseLotDimensionService.parseLotDimension).not.toHaveBeenCalled();
        expect(loggerWarnSpy).not.toHaveBeenCalled();
      });

      it('should handle error when ParseFreshdeskReviewsService throws exception', async () => {
        // Arrange
        const jobMessage: JobMessage = {
          jobKey: 'parseFreshDeskReview',
          payload: { path: '/invalid/path/file.json' }
        };

        const error = new Error('File not found');
        parseFreshdeskReviewsService.processReviewFile.mockRejectedValue(error);

        // Act & Assert
        await expect(service.processMessage(jobMessage)).rejects.toThrow('File not found');
        expect(parseFreshdeskReviewsService.processReviewFile).toHaveBeenCalledTimes(1);
        expect(parseFreshdeskReviewsService.processReviewFile).toHaveBeenCalledWith('/invalid/path/file.json');
        expect(parseLotDimensionService.parseLotDimension).not.toHaveBeenCalled();
      });

      it('should handle custom error object when ParseFreshdeskReviewsService rejects', async () => {
        // Arrange
        const jobMessage: JobMessage = {
          jobKey: 'parseFreshDeskReview',
          payload: { path: '/corrupted/file.json' }
        };

        const customError = { message: 'Parsing failed', code: 'PARSE_ERROR' };
        parseFreshdeskReviewsService.processReviewFile.mockRejectedValue(customError);

        // Act & Assert
        await expect(service.processMessage(jobMessage)).rejects.toBe(customError);
        expect(parseFreshdeskReviewsService.processReviewFile).toHaveBeenCalledTimes(1);
        expect(parseFreshdeskReviewsService.processReviewFile).toHaveBeenCalledWith('/corrupted/file.json');
      });
    });

    describe('unknown job key handling', () => {
      it('should log warning for unknown job key and not call any service', async () => {
        // Arrange
        const jobMessage: JobMessage = {
          jobKey: 'unknownJobKey',
          payload: { data: 'some data' }
        };

        // Act
        await service.processMessage(jobMessage);

        // Assert
        expect(loggerWarnSpy).toHaveBeenCalledTimes(1);
        expect(loggerWarnSpy).toHaveBeenCalledWith('Skipping unknown jobKey unknownJobKey');
        expect(parseLotDimensionService.parseLotDimension).not.toHaveBeenCalled();
        expect(parseFreshdeskReviewsService.processReviewFile).not.toHaveBeenCalled();
      });

      it('should log warning for empty job key', async () => {
        // Arrange
        const jobMessage: JobMessage = {
          jobKey: '',
          payload: { data: 'some data' }
        };

        // Act
        await service.processMessage(jobMessage);

        // Assert
        expect(loggerWarnSpy).toHaveBeenCalledTimes(1);
        expect(loggerWarnSpy).toHaveBeenCalledWith('Skipping unknown jobKey ');
        expect(parseLotDimensionService.parseLotDimension).not.toHaveBeenCalled();
        expect(parseFreshdeskReviewsService.processReviewFile).not.toHaveBeenCalled();
      });

      it('should log warning for null job key', async () => {
        // Arrange
        const jobMessage: JobMessage = {
          jobKey: null as any,
          payload: { data: 'some data' }
        };

        // Act
        await service.processMessage(jobMessage);

        // Assert
        expect(loggerWarnSpy).toHaveBeenCalledTimes(1);
        expect(loggerWarnSpy).toHaveBeenCalledWith('Skipping unknown jobKey null');
        expect(parseLotDimensionService.parseLotDimension).not.toHaveBeenCalled();
        expect(parseFreshdeskReviewsService.processReviewFile).not.toHaveBeenCalled();
      });

      it('should log warning for similar but incorrect job key', async () => {
        // Arrange
        const jobMessage: JobMessage = {
          jobKey: 'ParseLotDimension', // Wrong case
          payload: { data: 'some data' }
        };

        // Act
        await service.processMessage(jobMessage);

        // Assert
        expect(loggerWarnSpy).toHaveBeenCalledTimes(1);
        expect(loggerWarnSpy).toHaveBeenCalledWith('Skipping unknown jobKey ParseLotDimension');
        expect(parseLotDimensionService.parseLotDimension).not.toHaveBeenCalled();
        expect(parseFreshdeskReviewsService.processReviewFile).not.toHaveBeenCalled();
      });
    });

    describe('edge cases', () => {
      it('should handle message with undefined payload', async () => {
        // Arrange
        const jobMessage: JobMessage = {
          jobKey: 'parseLotDimension',
          payload: undefined as any
        };

        parseLotDimensionService.parseLotDimension.mockResolvedValue(undefined);

        // Act
        await service.processMessage(jobMessage);

        // Assert
        expect(parseLotDimensionService.parseLotDimension).toHaveBeenCalledTimes(1);
        expect(parseLotDimensionService.parseLotDimension).toHaveBeenCalledWith(undefined);
      });

      it('should handle message with null payload', async () => {
        // Arrange
        const jobMessage: JobMessage = {
          jobKey: 'parseLotDimension',
          payload: null as any
        };

        parseLotDimensionService.parseLotDimension.mockResolvedValue(undefined);

        // Act
        await service.processMessage(jobMessage);

        // Assert
        expect(parseLotDimensionService.parseLotDimension).toHaveBeenCalledTimes(1);
        expect(parseLotDimensionService.parseLotDimension).toHaveBeenCalledWith(null);
      });

      it('should handle parseFreshDeskReview with missing path property', async () => {
        // Arrange
        const jobMessage: JobMessage = {
          jobKey: 'parseFreshDeskReview',
          payload: { reviewId: '123' } // Missing path property
        };

        parseFreshdeskReviewsService.processReviewFile.mockResolvedValue('done');

        // Act
        await service.processMessage(jobMessage);

        // Assert
        expect(parseFreshdeskReviewsService.processReviewFile).toHaveBeenCalledTimes(1);
        expect(parseFreshdeskReviewsService.processReviewFile).toHaveBeenCalledWith('undefined');
      });

      it('should handle parseFreshDeskReview with null path property', async () => {
        // Arrange
        const jobMessage: JobMessage = {
          jobKey: 'parseFreshDeskReview',
          payload: { path: null as any }
        };

        parseFreshdeskReviewsService.processReviewFile.mockResolvedValue('done');

        // Act
        await service.processMessage(jobMessage);

        // Assert
        expect(parseFreshdeskReviewsService.processReviewFile).toHaveBeenCalledTimes(1);
        expect(parseFreshdeskReviewsService.processReviewFile).toHaveBeenCalledWith('null');
      });
    });
  });
});
