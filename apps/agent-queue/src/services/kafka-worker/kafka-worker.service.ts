import { Injectable, Logger } from '@nestjs/common';
import { JobMessage } from '../dtos/common-types';
import { ParseFreshdeskReviewsService } from '../parse-freshdesk-reviews/parse-freshdesk-reviews';
import { ParseLotDimensionService } from '../parse-lot-dimension/parse-lot-dimension';

@Injectable()
export class KafkaWorkerService {
  private readonly logger = new Logger(KafkaWorkerService.name);

  constructor(
    private readonly parseLotDimensionService: ParseLotDimensionService,
    private readonly parseFreshdeskReviews: ParseFreshdeskReviewsService
  ) {
    this.logger.log(`Start initializing Kafka Worker Service`);
  }

  async processMessage(message: JobMessage) {
    switch (message.jobKey) {
      case 'parseLotDimension':
        await this.parseLotDimensionService.parseLotDimension(message.payload);
        return;
      case 'parseFreshDeskReview':
        await this.parseFreshdeskReviews.processReviewFile(`${message.payload.path}`);
        return;
      default:
        this.logger.warn(`Skipping unknown jobKey ${message.jobKey}`);
        return;
    }
  }
}
