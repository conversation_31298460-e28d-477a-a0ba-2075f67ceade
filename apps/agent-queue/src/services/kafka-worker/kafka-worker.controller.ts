import { Controller, Logger, UseFilters } from '@nestjs/common';
import { EventPattern, Payload } from '@nestjs/microservices';
import { ChatMessagePayload } from '@app/common-shared/types/chat.types';
import configuration from '../../config/configuration';
import { KafkaMessageExceptionFilter } from '../../Exceptions/KafkaMessageExceptionFilter';
import { JobMessage } from '../dtos/common-types';
import { LivechatProcessorService } from '../livechat-processor/livechat-processor.service';
import { KafkaWorkerService } from './kafka-worker.service';

const config = configuration();

@Controller()
export class KafkaWorkerController {
  private readonly logger = new Logger(KafkaWorkerController.name);

  constructor(
    private readonly kafkaWorkerService: KafkaWorkerService,
    private readonly livechatProcessorService: LivechatProcessorService
  ) {}

  @EventPattern(config.kafka.topic)
  @UseFilters(new KafkaMessageExceptionFilter())
  async consume(@Payload() data: JobMessage) {
    this.kafkaWorkerService.processMessage(data).catch((e) => this.logger.error(`Error processing message: ${e}`));
  }

  @EventPattern(config.kafka.topicLiveChat)
  @UseFilters(new KafkaMessageExceptionFilter())
  async consumeLiveChat(@Payload() data: ChatMessagePayload) {
    this.livechatProcessorService
      .processChatMessage(data)
      .catch((e) => this.logger.error(`Error processing live chat message: ${e}`));
  }
}
