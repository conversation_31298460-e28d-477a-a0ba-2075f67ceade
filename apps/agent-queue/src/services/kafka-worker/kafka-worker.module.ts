import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { CommonSharedModule } from '@app/common-shared';
import configuration from '../../config/configuration';
import { LivechatProcessorService } from '../livechat-processor/livechat-processor.service';
import { ParseFreshdeskReviewsService } from '../parse-freshdesk-reviews/parse-freshdesk-reviews';
import { ParseLotDimensionService } from '../parse-lot-dimension/parse-lot-dimension';
import { KafkaWorkerController } from './kafka-worker.controller';
import { KafkaWorkerService } from './kafka-worker.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      cache: true,
      load: [configuration]
    }),
    CommonSharedModule
  ],
  controllers: [KafkaWorkerController],
  providers: [KafkaWorkerService, ParseLotDimensionService, ParseFreshdeskReviewsService, LivechatProcessorService]
})
export class KafkaWorkerModule {}
