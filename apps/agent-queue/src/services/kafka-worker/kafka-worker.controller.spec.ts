import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from '@nestjs/common';
import { KafkaWorkerController } from './kafka-worker.controller';
import { KafkaWorkerService } from './kafka-worker.service';
import { LivechatProcessorService } from '../livechat-processor/livechat-processor.service';
import { JobMessage } from '../dtos/common-types';
import { ChatMessagePayload } from '@app/common-shared/types/chat.types';

// Mock configuration module
jest.mock('../../config/configuration', () => ({
  __esModule: true,
  default: () => ({
    kafka: {
      topic: 'test-topic',
      topicLiveChat: 'test-livechat-topic'
    }
  })
}));

describe('KafkaWorkerController', () => {
  let controller: KafkaWorkerController;
  let kafkaWorkerService: jest.Mocked<KafkaWorkerService>;
  let livechatProcessorService: jest.Mocked<LivechatProcessorService>;
  let loggerSpy: jest.SpyInstance;

  beforeEach(async () => {
    const mockKafkaWorkerService = {
      processMessage: jest.fn()
    };

    const mockLivechatProcessorService = {
      processChatMessage: jest.fn()
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [KafkaWorkerController],
      providers: [
        {
          provide: KafkaWorkerService,
          useValue: mockKafkaWorkerService
        },
        {
          provide: LivechatProcessorService,
          useValue: mockLivechatProcessorService
        }
      ]
    }).compile();

    controller = module.get<KafkaWorkerController>(KafkaWorkerController);
    kafkaWorkerService = module.get(KafkaWorkerService);
    livechatProcessorService = module.get(LivechatProcessorService);

    // Mock the logger
    loggerSpy = jest.spyOn(controller['logger'], 'error').mockImplementation();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('consume', () => {
    it('should successfully process a job message', async () => {
      // Arrange
      const jobMessage: JobMessage = {
        jobKey: 'test-job-key',
        payload: { key1: 'value1', key2: 'value2' }
      };

      kafkaWorkerService.processMessage.mockResolvedValue(undefined);

      // Act
      await controller.consume(jobMessage);

      // Assert
      expect(kafkaWorkerService.processMessage).toHaveBeenCalledTimes(1);
      expect(kafkaWorkerService.processMessage).toHaveBeenCalledWith(jobMessage);
      expect(loggerSpy).not.toHaveBeenCalled();
    });

    it('should handle errors during message processing and log them', async () => {
      // Arrange
      const jobMessage: JobMessage = {
        jobKey: 'test-job-key',
        payload: { key1: 'value1' }
      };

      const errorMessage = 'Database connection failed';
      const error = new Error(errorMessage);
      kafkaWorkerService.processMessage.mockRejectedValue(error);

      // Act
      await controller.consume(jobMessage);

      // Wait for the catch block to execute
      await new Promise((resolve) => setTimeout(resolve, 0));

      // Assert
      expect(kafkaWorkerService.processMessage).toHaveBeenCalledTimes(1);
      expect(kafkaWorkerService.processMessage).toHaveBeenCalledWith(jobMessage);
      expect(loggerSpy).toHaveBeenCalledTimes(1);
      expect(loggerSpy).toHaveBeenCalledWith(`Error processing message: ${error}`);
    });

    it('should handle errors with string error messages', async () => {
      // Arrange
      const jobMessage: JobMessage = {
        jobKey: 'test-job-key',
        payload: { key1: 'value1' }
      };

      const errorMessage = 'String error message';
      kafkaWorkerService.processMessage.mockRejectedValue(errorMessage);

      // Act
      await controller.consume(jobMessage);

      // Wait for the catch block to execute
      await new Promise((resolve) => setTimeout(resolve, 0));

      // Assert
      expect(kafkaWorkerService.processMessage).toHaveBeenCalledTimes(1);
      expect(loggerSpy).toHaveBeenCalledTimes(1);
      expect(loggerSpy).toHaveBeenCalledWith(`Error processing message: ${errorMessage}`);
    });

    it('should process empty payload without errors', async () => {
      // Arrange
      const jobMessage: JobMessage = {
        jobKey: 'empty-job',
        payload: {}
      };

      kafkaWorkerService.processMessage.mockResolvedValue(undefined);

      // Act
      await controller.consume(jobMessage);

      // Assert
      expect(kafkaWorkerService.processMessage).toHaveBeenCalledTimes(1);
      expect(kafkaWorkerService.processMessage).toHaveBeenCalledWith(jobMessage);
      expect(loggerSpy).not.toHaveBeenCalled();
    });
  });

  describe('consumeLiveChat', () => {
    it('should successfully process a live chat message', async () => {
      // Arrange
      const chatMessagePayload: ChatMessagePayload = {
        eventId: 'event-123',
        threadId: 'thread-456',
        chatId: 'chat-789',
        senderId: 'sender-101',
        text: 'Hello, I need help with my account',
        eventType: 'message',
        timestamp: Date.now()
      };

      livechatProcessorService.processChatMessage.mockResolvedValue(undefined);

      // Act
      await controller.consumeLiveChat(chatMessagePayload);

      // Assert
      expect(livechatProcessorService.processChatMessage).toHaveBeenCalledTimes(1);
      expect(livechatProcessorService.processChatMessage).toHaveBeenCalledWith(chatMessagePayload);
      expect(loggerSpy).not.toHaveBeenCalled();
    });

    it('should handle errors during live chat message processing and log them', async () => {
      // Arrange
      const chatMessagePayload: ChatMessagePayload = {
        eventId: 'event-123',
        threadId: 'thread-456',
        chatId: 'chat-789',
        senderId: 'sender-101',
        text: 'Hello, I need help',
        eventType: 'message',
        timestamp: Date.now()
      };

      const errorMessage = 'Chat service unavailable';
      const error = new Error(errorMessage);
      livechatProcessorService.processChatMessage.mockRejectedValue(error);

      // Act
      await controller.consumeLiveChat(chatMessagePayload);

      // Wait for the catch block to execute
      await new Promise((resolve) => setTimeout(resolve, 0));

      // Assert
      expect(livechatProcessorService.processChatMessage).toHaveBeenCalledTimes(1);
      expect(livechatProcessorService.processChatMessage).toHaveBeenCalledWith(chatMessagePayload);
      expect(loggerSpy).toHaveBeenCalledTimes(1);
      expect(loggerSpy).toHaveBeenCalledWith(`Error processing live chat message: ${error}`);
    });

    it('should process system messages correctly', async () => {
      // Arrange
      const systemMessagePayload: ChatMessagePayload = {
        eventId: 'event-system-123',
        threadId: 'thread-456',
        chatId: 'chat-789',
        senderId: 'system',
        text: 'User joined the chat',
        eventType: 'system_message',
        systemMessageType: 'user_joined',
        timestamp: Date.now()
      };

      livechatProcessorService.processChatMessage.mockResolvedValue(undefined);

      // Act
      await controller.consumeLiveChat(systemMessagePayload);

      // Assert
      expect(livechatProcessorService.processChatMessage).toHaveBeenCalledTimes(1);
      expect(livechatProcessorService.processChatMessage).toHaveBeenCalledWith(systemMessagePayload);
      expect(loggerSpy).not.toHaveBeenCalled();
    });

    it('should handle errors with non-Error objects', async () => {
      // Arrange
      const chatMessagePayload: ChatMessagePayload = {
        eventId: 'event-123',
        threadId: 'thread-456',
        chatId: 'chat-789',
        senderId: 'sender-101',
        text: 'Test message',
        eventType: 'message',
        timestamp: Date.now()
      };

      const errorObject = { message: 'Custom error object', code: 500 };
      livechatProcessorService.processChatMessage.mockRejectedValue(errorObject);

      // Act
      await controller.consumeLiveChat(chatMessagePayload);

      // Wait for the catch block to execute
      await new Promise((resolve) => setTimeout(resolve, 0));

      // Assert
      expect(livechatProcessorService.processChatMessage).toHaveBeenCalledTimes(1);
      expect(loggerSpy).toHaveBeenCalledTimes(1);
      expect(loggerSpy).toHaveBeenCalledWith(`Error processing live chat message: ${errorObject}`);
    });

    it('should handle empty text messages', async () => {
      // Arrange
      const chatMessagePayload: ChatMessagePayload = {
        eventId: 'event-empty-text',
        threadId: 'thread-456',
        chatId: 'chat-789',
        senderId: 'sender-101',
        text: '',
        eventType: 'message',
        timestamp: Date.now()
      };

      livechatProcessorService.processChatMessage.mockResolvedValue(undefined);

      // Act
      await controller.consumeLiveChat(chatMessagePayload);

      // Assert
      expect(livechatProcessorService.processChatMessage).toHaveBeenCalledTimes(1);
      expect(livechatProcessorService.processChatMessage).toHaveBeenCalledWith(chatMessagePayload);
      expect(loggerSpy).not.toHaveBeenCalled();
    });
  });

  describe('constructor', () => {
    it('should be defined', () => {
      expect(controller).toBeDefined();
    });

    it('should have access to injected services', () => {
      expect(controller['kafkaWorkerService']).toBe(kafkaWorkerService);
      expect(controller['livechatProcessorService']).toBe(livechatProcessorService);
    });

    it('should initialize logger with correct name', () => {
      expect(controller['logger']).toBeInstanceOf(Logger);
    });
  });
});
