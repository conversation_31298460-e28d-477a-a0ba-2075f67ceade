import { Injectable, Logger } from '@nestjs/common';
import fs from 'node:fs';
import { WorkflowService } from '@app/common-shared/workflow.service';
import { ProgressTracker } from '../../utils/progress-tracker';

interface Question {
  question_id: string;
  question_sub: string;
  question_desc: string;
  question_status: string;
}

@Injectable()
export class ParseFreshdeskReviewsService {
  private readonly logger = new Logger(ParseFreshdeskReviewsService.name);

  constructor(private readonly workflowService: WorkflowService) {}

  parseCsvFileContent(csvContent: string, delimiter = ','): Question[] {
    // Split content into lines and remove empty lines
    const lines = csvContent.split(/\r?\n/).filter((line) => line.trim());

    if (lines.length === 0) {
      return [];
    }

    const result: Question[] = [];

    // Determine which lines to process
    const startIndex: number = 0;

    // Process each line
    for (let i = startIndex; i < lines.length; i++) {
      // Handle values that contain commas within quotes
      const row = [];
      let inQuotes = false;
      let currentValue = '';

      for (const char of lines[i]) {
        if (char === '"') {
          inQuotes = !inQuotes;
        } else if (char === delimiter && !inQuotes) {
          row.push(currentValue.trim());
          currentValue = '';
        } else {
          currentValue += char;
        }
      }

      // Push the last value
      row.push(currentValue.trim());

      if (row.length > 0 && Number(row[0]) > 0) {
        result.push({
          question_id: row[0],
          question_sub: row[1],
          question_desc: row[2],
          question_status: row[3]
        });
      }
    }

    return result;
  }

  async callWorkflow(input: string): Promise<string> {
    return this.workflowService.callWorkflow({
      question_obj_string: `${input}`
    });
  }

  async processReviewFile(filePath: string): Promise<string> {
    const content = fs.readFileSync(filePath, 'utf-8');
    const questions = this.parseCsvFileContent(content);
    const tracker = new ProgressTracker();
    const finalResult: { [key: string]: number } = tracker.getResult();
    let cnt: number = 0;
    for (const question of questions) {
      const runId = Number(question['question_id']);
      if (runId > 0 && !tracker.isProcessed(runId)) {
        this.logger.log(`iteration: ${runId}`);
        tracker.saveState(runId);
        cnt++;
        const response = await this.callWorkflow(JSON.stringify(question));
        const result = response
          .split('\n\n')
          .map((dataString) => {
            try {
              // Check if string starts with "data: "
              if (!dataString.startsWith('data: ')) {
                return null;
              }

              // Remove "data: " prefix and trim whitespace
              const dataObj = JSON.parse(dataString.slice(6).trim());

              return dataObj['event'] === 'workflow_finished' && dataObj['data']['status'] === 'succeeded'
                ? (dataObj['data']['outputs']['final_result'] as string)
                : null;
            } catch (e) {
              return null;
            }
          })
          .filter((line) => line !== null)
          .join('');
        if (finalResult[result] === undefined) {
          finalResult[result] = 1;
        } else {
          finalResult[result] = finalResult[result] + 1;
        }
        if (cnt % 50 === 0) {
          this.logger.log(`write cached result at: ${runId}`);
          tracker.writeResult(finalResult);
        }
      } else {
        this.logger.log(`skip: ${runId}`);
      }
    }

    this.logger.log(`response: ${JSON.stringify(finalResult, null, 2)}`);

    return 'done';
  }
}
