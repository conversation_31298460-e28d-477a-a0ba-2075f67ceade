import Anthropic from '@anthropic-ai/sdk';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class ParseLotDimensionService {
  constructor(private configService: ConfigService) {}

  private readonly logger = new Logger(ParseLotDimensionService.name);

  private readonly anthropic = new Anthropic({
    apiKey: this.configService.get<string>(`llm.Claude.apiKey`)
  });

  async parseLotDimension(payload: { [key: string]: string }): Promise<void> {
    const message = await this.anthropic.messages.create({
      model: 'claude-3-5-sonnet-20241022',
      // stream: true,
      max_tokens: 1024,
      system: [
        {
          type: 'text',
          text: `You're a professional realtor can answer user questions about real estate market.`
        }
      ],
      messages: [
        {
          role: 'user',
          content: ` Take the following input, it is describing width and depth of a land parcel.
Then output in JSON format with keys: "width" (land width in number), "depth" (land depth in number) and "unit" (values in "feet" or "meter").

Example output should be like below json:
{"width": 50.2, "depth": 90, "unit", "feet"}

Convert measure not in imperial units into feet.
${payload.data}`
        }
      ]
    });

    this.logger.log(`message: ${JSON.stringify(message.content)}`);
  }
}
