import { ChatSessionService } from '@app/common-shared/chat-session.service';
import { LiveChatService } from '@app/common-shared/livechat.service';
import { WorkflowService } from '@app/common-shared/workflow.service';
import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { ChatMessagePayload } from '@app/common-shared/types/chat.types';
import { LivechatProcessorService } from './livechat-processor.service';

describe('LivechatProcessorService', () => {
  let service: LivechatProcessorService;
  let configService: jest.Mocked<ConfigService>;
  let workflowService: jest.Mocked<WorkflowService>;
  let chatSessionService: jest.Mocked<ChatSessionService>;
  let liveChatService: jest.Mocked<LiveChatService>;

  const mockAgentUserId = 'agent-123';

  const createMockChatMessagePayload = (overrides: Partial<ChatMessagePayload> = {}): ChatMessagePayload => ({
    eventId: 'event-123',
    threadId: 'thread-123',
    chatId: 'chat-123',
    senderId: 'user-123',
    text: 'Hello, I need help',
    eventType: 'message',
    timestamp: Date.now(),
    ...overrides
  });

  beforeEach(async () => {
    const mockConfigService = {
      get: jest.fn()
    };

    const mockWorkflowService = {
      callWorkflow: jest.fn()
    };

    const mockChatSessionService = {
      fetchActiveThread: jest.fn(),
      createThenReturnThread: jest.fn(),
      addEventToThread: jest.fn(),
      checkDuplicateEvent: jest.fn(),
      setPendingEventLock: jest.fn(),
      updateThreadIntention: jest.fn(),
      closeThread: jest.fn(),
      releasePendingEventLock: jest.fn()
    };

    const mockLiveChatService = {
      processChat: jest.fn()
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        LivechatProcessorService,
        {
          provide: ConfigService,
          useValue: mockConfigService
        },
        {
          provide: WorkflowService,
          useValue: mockWorkflowService
        },
        {
          provide: ChatSessionService,
          useValue: mockChatSessionService
        },
        {
          provide: LiveChatService,
          useValue: mockLiveChatService
        }
      ]
    }).compile();

    service = module.get<LivechatProcessorService>(LivechatProcessorService);
    configService = module.get(ConfigService) as jest.Mocked<ConfigService>;
    workflowService = module.get(WorkflowService) as jest.Mocked<WorkflowService>;
    chatSessionService = module.get(ChatSessionService) as jest.Mocked<ChatSessionService>;
    liveChatService = module.get(LiveChatService) as jest.Mocked<LiveChatService>;

    configService.get.mockImplementation((key: string, defaultValue?: unknown) => {
      if (key === 'liveChat.agentUserId') return mockAgentUserId;
      if (key === 'liveChat.accessToken') return 'access-token-123';
      return defaultValue;
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('processChatMessage', () => {
    describe('when processing user messages', () => {
      it('should process user message and trigger workflow', async () => {
        // Arrange
        const payload = createMockChatMessagePayload();
        const mockThread = {
          id: 'thread-123',
          sessionId: 'session-123',
          status: 'active' as const,
          createdAt: new Date(),
          events: [
            {
              id: 'event-123',
              threadId: 'thread-123',
              senderId: 'user-123',
              type: 'message' as const,
              content: 'Hello, I need help',
              timestamp: new Date()
            }
          ]
        };

        const mockWorkflowResponse = `data: {"event":"workflow_finished","data":{"status":"succeeded","outputs":{"output":"{\\"intention\\":\\"unlockAccount\\",\\"text\\":\\"I can help you unlock your account\\",\\"status\\":\\"incomplete\\"}"}}}\n\n`;

        chatSessionService.checkDuplicateEvent.mockResolvedValue(false);
        chatSessionService.fetchActiveThread.mockResolvedValue(undefined);
        chatSessionService.createThenReturnThread.mockResolvedValue(mockThread);
        chatSessionService.addEventToThread.mockResolvedValue(undefined);
        chatSessionService.setPendingEventLock.mockResolvedValue(undefined);
        chatSessionService.updateThreadIntention.mockResolvedValue(undefined);
        chatSessionService.releasePendingEventLock.mockResolvedValue(undefined);
        workflowService.callWorkflow.mockResolvedValue(mockWorkflowResponse);
        liveChatService.processChat.mockResolvedValue(undefined);

        // Act
        await service.processChatMessage(payload);

        // Assert
        expect(chatSessionService.checkDuplicateEvent).toHaveBeenCalled();
        expect(chatSessionService.createThenReturnThread).toHaveBeenCalledWith('thread-123', 'chat-123');
        expect(workflowService.callWorkflow).toHaveBeenCalledWith(
          { userMessage: 'customer:\nHello, I need help' },
          'agent2'
        );
        expect(chatSessionService.updateThreadIntention).toHaveBeenCalledWith('thread-123', 'unlockAccount');
        expect(liveChatService.processChat).toHaveBeenCalledWith('chat-123', 'I can help you unlock your account');
      });

      it('should not process duplicate events', async () => {
        // Arrange
        const payload = createMockChatMessagePayload();
        chatSessionService.checkDuplicateEvent.mockResolvedValue(true);

        // Act
        await service.processChatMessage(payload);

        // Assert
        expect(chatSessionService.fetchActiveThread).not.toHaveBeenCalled();
        expect(workflowService.callWorkflow).not.toHaveBeenCalled();
      });

      it('should ignore agent messages', async () => {
        // Arrange
        const payload = createMockChatMessagePayload({
          senderId: mockAgentUserId
        });
        const mockThread = {
          id: 'thread-123',
          sessionId: 'session-123',
          status: 'active' as const,
          createdAt: new Date(),
          events: []
        };

        chatSessionService.checkDuplicateEvent.mockResolvedValue(false);
        chatSessionService.fetchActiveThread.mockResolvedValue(mockThread);
        chatSessionService.addEventToThread.mockResolvedValue(undefined);

        // Act
        await service.processChatMessage(payload);

        // Assert
        expect(workflowService.callWorkflow).not.toHaveBeenCalled();
        expect(chatSessionService.setPendingEventLock).not.toHaveBeenCalled();
      });
    });

    describe('when processing system messages', () => {
      it('should close thread for system messages', async () => {
        // Arrange
        const payload = createMockChatMessagePayload({
          eventType: 'system_message',
          systemMessageType: 'routing.archived_inactive'
        });

        chatSessionService.closeThread.mockResolvedValue(undefined);

        // Act
        await service.processChatMessage(payload);

        // Assert
        expect(chatSessionService.closeThread).toHaveBeenCalledWith('thread-123');
      });
    });

    describe('error handling', () => {
      it('should handle workflow service errors gracefully', async () => {
        // Arrange
        const payload = createMockChatMessagePayload();
        const mockThread = {
          id: 'thread-123',
          sessionId: 'session-123',
          status: 'active' as const,
          createdAt: new Date(),
          events: [
            {
              id: 'event-123',
              threadId: 'thread-123',
              senderId: 'user-123',
              type: 'message' as const,
              content: 'Hello, I need help',
              timestamp: new Date()
            }
          ]
        };

        chatSessionService.checkDuplicateEvent.mockResolvedValue(false);
        chatSessionService.fetchActiveThread.mockResolvedValue(undefined);
        chatSessionService.createThenReturnThread.mockResolvedValue(mockThread);
        chatSessionService.addEventToThread.mockResolvedValue(undefined);
        chatSessionService.setPendingEventLock.mockResolvedValue(undefined);
        workflowService.callWorkflow.mockRejectedValue(new Error('Workflow service error'));

        // Act - Should not throw, but log error and continue
        await expect(service.processChatMessage(payload)).resolves.not.toThrow();

        // Assert
        expect(workflowService.callWorkflow).toHaveBeenCalled();
        expect(chatSessionService.setPendingEventLock).toHaveBeenCalled();
      });
    });
  });
});
