import { ChatSessionService } from '@app/common-shared/chat-session.service';
import { LiveChatService } from '@app/common-shared/livechat.service';
import { WorkflowService } from '@app/common-shared/workflow.service';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ChatEvent, ChatIntention, ChatThread, ChatMessagePayload } from '@app/common-shared/types/chat.types';

@Injectable()
export class LivechatProcessorService {
  private readonly logger = new Logger(LivechatProcessorService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly workflowService: WorkflowService,
    private readonly chatSessionService: ChatSessionService,
    private readonly liveChatService: LiveChatService
  ) {
    this.logger.log('LivechatProcessorService initialized');
  }

  async processChatMessage(payload: ChatMessagePayload) {
    const agentUserId = this.configService.get(`liveChat.agentUserId`, '');

    try {
      this.logger.log(`Processing chat message: ${payload.eventId}`);

      if (payload.eventType === 'message') {
        const thread = await this.processMessageEvent(payload, agentUserId);
        if (thread === undefined) {
          this.logger.log(`Duplicate event: ${payload.eventId}`);
          return;
        }

        this.logger.log(`Event message: ${payload.text}`);
        this.logger.log(`Thread detail: ${JSON.stringify(thread.events, null, 2)}`);

        if (payload.senderId !== agentUserId) {
          if (thread.pendingEventId === undefined) {
            await this.chatSessionService.setPendingEventLock(thread.id, payload.eventId);

            const formatedThread = thread.events
              .map((item) => `${item.senderId === agentUserId ? 'assistant' : 'customer'}:\n${item.content}`)
              .join('\n');

            const resultObj = await this.fetchResponseFromAgenticWorkflow(thread.intention, formatedThread);
            if (resultObj !== undefined) {
              if (thread.intention === undefined && resultObj.intention !== 'other') {
                await this.chatSessionService.updateThreadIntention(thread.id, resultObj.intention);
              }
              await this.liveChatService.processChat(payload.chatId, resultObj.text);
              if (resultObj.status === 'resolved') {
                await this.chatSessionService.closeThread(thread.id);
              } else {
                await this.chatSessionService.releasePendingEventLock(thread.id);
              }
            }
          }
        } else {
          this.logger.log(`Agent event received, ignore`);
        }
      } else if (payload.eventType === 'system_message' && payload.systemMessageType) {
        await this.chatSessionService.closeThread(payload.threadId);
      } else {
        this.logger.log(`Unhandled event type: ${payload.eventType}`);
      }
    } catch (error) {
      this.logger.error(`Error processing chat message: ${error}`);
      throw error;
    }
  }

  private async processMessageEvent(payload: ChatMessagePayload, agentUserId: string): Promise<ChatThread | undefined> {
    const chatEvent: ChatEvent = {
      id: payload.eventId,
      threadId: payload.threadId,
      senderId: payload.senderId,
      type: 'message',
      content: payload.text,
      timestamp: new Date(payload.timestamp) // Convert Unix timestamp back to Date
    };

    const isDuplicateEvent = await this.chatSessionService.checkDuplicateEvent(payload.threadId, chatEvent);
    if (isDuplicateEvent) {
      return undefined;
    }

    let thread = await this.chatSessionService.fetchActiveThread(payload.threadId);
    if (thread === undefined && payload.senderId !== agentUserId) {
      thread = await this.chatSessionService.createThenReturnThread(payload.threadId, payload.chatId);
    }

    await this.chatSessionService.addEventToThread(payload.threadId, chatEvent);
    return thread;
  }

  private async fetchResponseFromAgenticWorkflow(intention: ChatIntention | undefined, userMessage: string) {
    try {
      const response = await this.workflowService.callWorkflow(
        intention === undefined ? { userMessage } : { intention, userMessage },
        'agent2'
      );

      const result = response
        .split('\n\n')
        .map((dataString) => {
          try {
            if (!dataString.startsWith('data: ')) {
              return null;
            }

            const dataObj = JSON.parse(dataString.slice(6).trim());
            return dataObj['event'] === 'workflow_finished' && dataObj['data']['status'] === 'succeeded'
              ? (dataObj['data']['outputs']['output'] as string)
              : null;
          } catch (e) {
            return null;
          }
        })
        .filter((line) => line !== null)
        .join('');

      this.logger.log(`Agent output: ${result}`);
      return JSON.parse(result) as {
        intention: ChatIntention;
        text: string;
        status: 'resolved' | 'incomplete';
      };
    } catch (e) {
      this.logger.error(`Call dify error: ${e}`);
      return undefined;
    }
  }
}
