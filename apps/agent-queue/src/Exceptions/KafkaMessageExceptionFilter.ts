import { ArgumentsHost, Catch, Logger } from '@nestjs/common';
import { BaseExceptionFilter } from '@nestjs/core';
import { KafkaContext } from '@nestjs/microservices';

@Catch()
export class KafkaMessageExceptionFilter extends BaseExceptionFilter {
  private readonly logger = new Logger(KafkaMessageExceptionFilter.name);

  async catch(exception: unknown, host: ArgumentsHost) {
    const kafkaContext = host.switchToRpc().getContext<KafkaContext>();
    const message = kafkaContext.getMessage();

    this.logger.error(`Error in Kafka message processing: ${exception}`);

    try {
      await this.commitOffset(kafkaContext);
      this.logger.error(`Skipping wrong format message: ${message.key}`);
    } catch (e) {
      this.logger.error(`Failed to commit message ${message.key} offset: ${e}`);
    }

    return;
  }

  private async commitOffset(context: KafkaContext): Promise<void> {
    const consumer = context.getConsumer && context.getConsumer();

    if (!consumer) {
      throw new Error('Consumer instance is not available from KafkaContext.');
    }

    const topic = context.getTopic && context.getTopic();
    const partition = context.getPartition && context.getPartition();
    const message = context.getMessage();
    const offset = message.offset;

    if (!topic || partition === undefined || offset === undefined) {
      throw new Error('Incomplete Kafka message context for committing offset.');
    }

    await consumer.commitOffsets([
      {
        topic,
        partition,
        offset: (Number(offset) + 1).toString()
      }
    ]);
  }
}
