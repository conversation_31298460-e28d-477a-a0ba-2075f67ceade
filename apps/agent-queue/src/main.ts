import { Logger, ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import * as process from 'node:process';
import configuration from './config/configuration';
import { KafkaWorkerModule } from './services/kafka-worker/kafka-worker.module';

const logger = new Logger('Bootstrap');
const config = configuration();

async function bootstrap() {
  const app = await NestFactory.createMicroservice<MicroserviceOptions>(KafkaWorkerModule, {
    transport: Transport.KAFKA,
    options: config.kafka.options
  });

  app.useGlobalPipes(
    new ValidationPipe({
      disableErrorMessages: false
    })
  );

  await app.listen();
}

bootstrap()
  .then(() => {
    logger.log('Kafka worker started successfully');
  })
  .catch((e) => {
    logger.error(`Failed to start Kafka worker: ${e}`);
    process.exit(1);
  });
