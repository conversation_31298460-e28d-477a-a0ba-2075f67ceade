import fs from 'fs';

interface ProgressState {
  processed: number[];
  result: { [key: string]: number };
}

class ProgressTracker {
  private filename: string;
  private state: ProgressState;

  constructor(filename: string = './progress.json') {
    this.filename = filename;
    this.state = this.loadState();
  }

  private loadState(): ProgressState {
    try {
      const data: string = fs.readFileSync(this.filename, 'utf8');
      return JSON.parse(data) as ProgressState;
    } catch (e) {
      const newState: ProgressState = {
        processed: [],
        result: {}
      };
      fs.writeFileSync(this.filename, JSON.stringify(newState, null, 2));
      return newState;
    }
  }

  public saveState(index: number): void {
    this.state.processed.push(index);
    fs.writeFileSync(this.filename, JSON.stringify(this.state, null, 2));
  }

  public writeResult(result: { [key: string]: number }): void {
    this.state.result = result;
    fs.writeFileSync(this.filename, JSON.stringify(this.state, null, 2));
  }

  public getResult() {
    return this.state.result;
  }

  public isProcessed(index: number): boolean {
    return this.state.processed.includes(index);
  }
}

export { ProgressState, ProgressTracker };
