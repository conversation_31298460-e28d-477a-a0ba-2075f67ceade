# NestJS Unit Testing
Unit testing in NestJS applications focuses on testing individual components in isolation.

Follow the instructions from @docs/testing/unit_testing_services.md, which covers comprehensive testing strategies using Jest framework following the **Arrange, Act, Assert (AAA)** paradigm.

Refer to the best practices code snippets from @docs/testing/unit_testing_examples.md for different scenarios.

Create a new or update the existing unit test spec file in the same folder along with $ARGUMENTS

Use the test-expert subagent to fix failing tests.

Do not touch other test spec files.
