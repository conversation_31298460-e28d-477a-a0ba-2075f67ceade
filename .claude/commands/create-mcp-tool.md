Follow the instructions from @docs/mcp folder, create a new nestjs module under application 'mcp-tools' src/module/tools that can be used as a streamable http mcp server.

Use the existing module @apps/mcp-tools/src/modules/tools/everything as an example, create nestjs standard module and controller, then put the main mcp server logic in a separate file.

At the end, add the new module to the tools module @apps/mcp-tools/src/modules/tools/tools.module.ts

The new mcp server can do things as following: $ARGUMENTS
