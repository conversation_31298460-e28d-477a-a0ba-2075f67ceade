{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "apps/data-gateway/src", "compilerOptions": {"deleteOutDir": true, "webpack": true, "tsConfigPath": "apps/data-gateway/tsconfig.app.json"}, "monorepo": true, "root": "apps/data-gateway", "projects": {"data-gateway": {"type": "application", "root": "apps/data-gateway", "entryFile": "main", "sourceRoot": "apps/data-gateway/src", "compilerOptions": {"tsConfigPath": "apps/data-gateway/tsconfig.app.json"}}, "agent-queue": {"type": "application", "root": "apps/agent-queue", "entryFile": "main", "sourceRoot": "apps/agent-queue/src", "compilerOptions": {"tsConfigPath": "apps/agent-queue/tsconfig.app.json"}}, "agent-restful": {"type": "application", "root": "apps/agent-restful", "entryFile": "main", "sourceRoot": "apps/agent-restful/src", "compilerOptions": {"tsConfigPath": "apps/agent-restful/tsconfig.app.json"}}, "mcp-tools": {"type": "application", "root": "apps/mcp-tools", "entryFile": "main", "sourceRoot": "apps/mcp-tools/src", "compilerOptions": {"tsConfigPath": "apps/mcp-tools/tsconfig.app.json"}}, "common-shared": {"type": "library", "root": "libs/common-shared", "entryFile": "index", "sourceRoot": "libs/common-shared/src", "compilerOptions": {"tsConfigPath": "libs/common-shared/tsconfig.lib.json"}}}}